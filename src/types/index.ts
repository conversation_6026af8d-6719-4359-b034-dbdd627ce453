// Multi-tenant types
export interface Tenant {
  id: string;
  name: string;
  slug: string;
  settings: TenantSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface TenantSettings {
  theme?: {
    primaryColor?: string;
    logo?: string;
  };
  features?: {
    enableVoiceInput?: boolean;
    enableFileUpload?: boolean;
  };
}

// User and Authentication types
export interface User {
  id: string;
  email: string;
  name: string;
  tenantId: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export type UserRole = 'admin' | 'user' | 'viewer';

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  tenant: Tenant | null;
}

// Project types
export interface Project {
  id: string;
  name: string;
  description: string;
  tenantId: string;
  assets: ProjectAsset[];
  sessions: ChatSession[];
  createdAt: Date;
  updatedAt: Date;
  lastActivity: Date;
}

export interface ProjectAsset {
  id: string;
  type: AssetType;
  name: string;
  url: string;
  metadata: Record<string, unknown>;
  createdAt: Date;
}

export type AssetType = 'codebase' | 'document' | 'figma' | 'image' | 'other';

// Chat types
export interface ChatSession {
  id: string;
  projectId: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  sessionId: string;
  role: MessageRole;
  content: string;
  metadata?: MessageMetadata;
  createdAt: Date;
}

export type MessageRole = 'user' | 'assistant' | 'system';

export interface MessageMetadata {
  attachments?: Attachment[];
  codeBlocks?: CodeBlock[];
  references?: Reference[];
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  url: string;
  size: number;
}

export interface CodeBlock {
  language: string;
  code: string;
  filename?: string;
}

export interface Reference {
  type: 'file' | 'document' | 'url';
  name: string;
  url: string;
}

// UI State types
export type Theme = 'light' | 'dark' | 'system';

export interface UIState {
  sidebarOpen: boolean;
  activeTab: string;
  theme: Theme;
  notifications: Notification[];
}

// Sidebar types
export interface SidebarItem {
  id: string;
  type: 'nav' | 'project' | 'chat' | 'separator';
  label?: string;
  icon?: string;
  href?: string;
  badge?: string | number;
  isActive?: boolean;
  items?: SidebarItem[];
}

export interface SidebarSection {
  id: string;
  title?: string;
  items: SidebarItem[];
  collapsible?: boolean;
  collapsed?: boolean;
}

export interface SidebarState {
  isOpen: boolean;
  isExpanded: boolean;
  width: number;
  collapsedWidth: number;
  sections: SidebarSection[];
  activeItem?: string;
  isPinned: boolean;
}

export interface SidebarContextValue {
  state: SidebarState;
  toggle: () => void;
  expand: () => void;
  collapse: () => void;
  open: () => void;
  close: () => void;
  setWidth: (width: number) => void;
  setActiveItem: (itemId: string) => void;
  togglePin: () => void;
  updateSection: (sectionId: string, updates: Partial<SidebarSection>) => void;
  addItem: (sectionId: string, item: SidebarItem, index?: number) => void;
  removeItem: (sectionId: string, itemId: string) => void;
}

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  createdAt: Date;
  read: boolean;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface ProjectForm {
  name: string;
  description: string;
}

export interface MessageForm {
  content: string;
  attachments?: File[];
}
