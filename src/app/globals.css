@import 'tailwindcss';

/* ==============================
   Kavia AI Theme Variables
   ============================== */
:root {
  /* Light Theme (HSL values) */
  --background: 0 0% 100%;
  --foreground: 222.2 47.4% 11.2%;

  --card: 0 0% 100%;
  --card-foreground: 222.2 47.4% 11.2%;

  --popover: 0 0% 100%;
  --popover-foreground: 222.2 47.4% 11.2%;

  /* Brand Orange */
  --primary: 24 95% 53%;
  --primary-foreground: 0 0% 100%;

  /* Secondary */
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 47.4% 11.2%;

  /* Muted */
  --muted: 210 40% 96%;
  --muted-foreground: 215 20.2% 65.1%;

  /* Accent */
  --accent: 210 40% 96%;
  --accent-foreground: 217.2 32.6% 17.5%;

  /* Status Colors */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --success: 142.1 70.6% 45.3%;
  --success-foreground: 0 0% 100%;
  --warning: 38 92% 50%;
  --warning-foreground: 0 0% 100%;

  /* Borders & Inputs */
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 24 95% 53%;

  /* Background Gradients */
  --bg-gradient-from: 0 0% 100%;
  --bg-gradient-to: 24 100% 98%;

  /* Charts */
  --chart-1: 24 95% 53%;
  --chart-2: 28 96% 61%;
  --chart-3: 32 97% 72%;
  --chart-4: 20 92% 37%;
  --chart-5: 16 88% 29%;

  /* Radius */
  --radius: 0.5rem;
}

/* ==============================
   Dark Theme
   ============================== */
.dark {
  /* Dark Theme (HSL values) */
  --background: 24 14% 10%;
  --foreground: 0 0% 98%;

  --card: 24 14% 14%;
  --card-foreground: 0 0% 98%;

  --popover: 24 14% 14%;
  --popover-foreground: 0 0% 98%;

  --primary: 28 96% 61%;
  --primary-foreground: 24 14% 10%;

  --secondary: 24 6% 18%;
  --secondary-foreground: 0 0% 98%;

  --muted: 24 6% 18%;
  --muted-foreground: 215 20.2% 65.1%;

  --accent: 215 24% 27%;
  --accent-foreground: 0 0% 100%;

  --destructive: 0 92% 62%;
  --destructive-foreground: 0 0% 100%;

  --success: 142.1 70.6% 45.3%;
  --success-foreground: 24 14% 10%;

  --warning: 38 92% 50%;
  --warning-foreground: 24 14% 10%;

  --border: 24 6% 25%;
  --input: 24 6% 18%;
  --ring: 28 96% 61%;

  /* Background Gradients */
  --bg-gradient-from: 24 14% 10%;
  --bg-gradient-to: 24 14% 16%;

  --chart-1: 28 96% 61%;
  --chart-2: 32 97% 72%;
  --chart-3: 36 97% 88%;
  --chart-4: 20 92% 37%;
  --chart-5: 16 88% 29%;
}

/* ==============================
   Native UI color-scheme
   ============================== */
html {
  color-scheme: light;
}
html.dark {
  color-scheme: dark;
}
/* ==============================
   Base
   ============================== */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  background-image: linear-gradient(
    180deg,
    hsl(var(--bg-gradient-from)) 0%,
    hsl(var(--bg-gradient-to)) 100%
  );
  background-attachment: fixed;
  color: hsl(var(--foreground));
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    sans-serif;
  font-feature-settings:
    'rlig' 1,
    'calt' 1;
}

/* ==============================
   Radial Background Pattern
   ============================== */
body::before {
  content: '';
  position: fixed;
  inset: 0;
  background:
    radial-gradient(
      800px circle at 10% 10%,
      hsl(var(--primary) / 0.03),
      transparent 60%
    ),
    radial-gradient(
      600px circle at 90% 5%,
      hsl(var(--primary) / 0.02),
      transparent 55%
    ),
    radial-gradient(
      700px circle at 50% 90%,
      hsl(var(--accent) / 0.02),
      transparent 60%
    );
  pointer-events: none;
  z-index: -1;
}

.dark body::before {
  background:
    radial-gradient(
      900px circle at 10% 10%,
      hsl(var(--primary) / 0.12),
      transparent 60%
    ),
    radial-gradient(
      700px circle at 90% 0%,
      hsl(var(--primary) / 0.08),
      transparent 55%
    ),
    radial-gradient(
      800px circle at 50% 90%,
      hsl(var(--accent) / 0.06),
      transparent 60%
    );
}
