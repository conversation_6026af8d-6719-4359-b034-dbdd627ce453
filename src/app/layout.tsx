import type { Metadata } from 'next';
import { Inter, JetBrains_Mono } from 'next/font/google';
import Script from 'next/script';
import './globals.css';
import { ThemeProvider } from '@/components/theme/theme-provider';
import { HydrationFix } from '@/components/hydration-fix';

const inter = Inter({
  variable: '--font-sans',
  subsets: ['latin'],
});

const jetbrainsMono = JetBrains_Mono({
  variable: '--font-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Kavia Chatbot',
  description: 'AI-powered chatbot for multi-tenant project management',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased`}
        suppressHydrationWarning
      >
        <Script
          id="theme-init"
          strategy="beforeInteractive"
        >{`(function(){try{var s='kavia-theme';var t=localStorage.getItem(s);var e=document.documentElement;var m=window.matchMedia('(prefers-color-scheme: dark)');var th=t==='dark'||(t!=='light'&&m.matches)?'dark':'light';if(th==='dark'){e.classList.add('dark');}else{e.classList.remove('dark');}}catch(_){}})();`}</Script>
        <Script
          id="hydration-fix"
          strategy="afterInteractive"
        >{`(function(){try{var body=document.body;if(body&&body.getAttribute('data-demoway-document-id')){body.removeAttribute('data-demoway-document-id');}}catch(_){}})();`}</Script>
        <ThemeProvider defaultTheme="system" storageKey="kavia-theme">
          <HydrationFix />
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
