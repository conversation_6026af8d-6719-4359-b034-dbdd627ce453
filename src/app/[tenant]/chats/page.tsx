'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { ChatHistory } from '@/components/content/chat-history';

interface ChatsPageProps {
  params: Promise<{
    tenant: string;
  }>;
}

export default function ChatsPage({ params }: ChatsPageProps) {
  const router = useRouter();
  const [tenant, setTenant] = useState<string>('');

  useEffect(() => {
    params.then(p => {
      setTenant(p.tenant);
    });
  }, [params]);

  const handleChatClick = (chatId: string) => {
    // Navigate to specific chat interface
    if (tenant) {
      router.push(`/${tenant}/chats/${chatId}`);
    }
  };

  const handleNewChat = () => {
    // Navigate to new chat creation
    if (tenant) {
      router.push(`/${tenant}/newchat`);
    }
  };

  if (!tenant) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  return (
    <ChatHistory
      onChatClick={handleChatClick}
      onNewChat={handleNewChat}
    />
  );
}
