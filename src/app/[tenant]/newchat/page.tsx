'use client';

import { useEffect, useState } from 'react';
import { NewChat } from '@/components/content/new-chat';

interface NewChatPageProps {
  params: Promise<{
    tenant: string;
  }>;
}

export default function NewChatPage({ params }: NewChatPageProps) {
  const [tenant, setTenant] = useState<string>('');

  useEffect(() => {
    params.then(p => {
      setTenant(p.tenant);
    });
  }, [params]);

  if (!tenant) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  return <NewChat />;
}
