'use client';

import { useParams } from 'next/navigation';
import { useEffect } from 'react';
import ChatView from '@/components/content/chat-view';
import { useChatStore } from '@/store/chat';

interface ProjectChatPageProps {
  params: Promise<{
    tenant: string;
    projectId: string;
    chatId: string;
  }>;
}

export default function ProjectChatPage({ params }: ProjectChatPageProps) {
  const resolvedParams = useParams<{ tenant: string; projectId: string; chatId: string }>();
  const tenant = resolvedParams?.tenant as string;
  const projectId = resolvedParams?.projectId as string;
  const chatId = resolvedParams?.chatId as string;
  const loadChatHistory = useChatStore((s) => s.loadChatHistory);
  const reset = useChatStore((s) => s.reset);

  useEffect(() => {
    // Reset the chat store first
    reset();
    
    // Load the chat history for this specific project chat
    if (chatId && projectId) {
      loadChatHistory(chatId, projectId);
    }
  }, [chatId, projectId, loadChatHistory, reset]);

  if (!tenant || !projectId || !chatId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-lg font-medium text-gray-900 mb-2">Loading project chat...</div>
          <div className="text-sm text-gray-500">Please wait while we load your conversation</div>
        </div>
      </div>
    );
  }

  return <ChatView chatId={chatId} projectId={projectId} />;
}
