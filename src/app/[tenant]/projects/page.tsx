'use client';

import { useRouter, useParams } from 'next/navigation';
import { Projects } from '@/components/content/projects';

export default function ProjectsPage() {
  const router = useRouter();
  const params = useParams<{ tenant: string }>();
  const tenant = (params?.tenant as string) || '';

  const handleProjectClick = (projectId: string) => {
    if (!tenant) return;
    router.push(`/${tenant}/${projectId}/chat`);
  };

  const handleNewProject = () => {
    // Handle new project creation
    console.log('Create new project');
    // In production, you might show a modal or navigate to a creation page
  };

  if (!tenant) return null;

  return (
    <Projects
      onProjectClick={handleProjectClick}
      onNewProject={handleNewProject}
    />
  );
}
