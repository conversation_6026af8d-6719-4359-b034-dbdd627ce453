'use client';

import { useRouter, useParams } from 'next/navigation';
import { ProjectChatHistory } from '@/components/content/project-chat-history';
 
export default function ChatPage() {
  const router = useRouter();
  const params = useParams<{ tenant: string; project: string }>();
  const tenant = (params?.tenant as string) || '';
  const projectId = (params?.project as string) || '';

  const handleChatClick = (chatId: string) => {
    // Navigate to specific project chat interface
    if (tenant && projectId) {
      // Convert project ID to professional format
      const professionalProjectId = getProfessionalProjectId(projectId);
      router.push(`/${tenant}/projects/${professionalProjectId}/chats/${chatId}`);
    }
  };

  // Convert project IDs to professional format
  const getProfessionalProjectId = (projectId: string): string => {
    const projectMapping: { [key: string]: string } = {
      'project-1': 'banking-app',
      'project-2': 'ecommerce-platform',
      'project-3': 'analytics-dashboard',
      'project-4': 'social-media-app',
      'project-5': 'task-management-tool',
      'project-6': 'weather-app-widget',
      'project-7': 'chat-application',
      'project-8': 'portfolio-website',
      'project-9': 'blog-cms-system',
      'project-10': 'inventory-management',
      'project-11': 'customer-support-bot',
      'project-12': 'video-streaming-app',
      'project-13': 'e-learning-platform',
      'project-14': 'food-delivery-app',
      'project-15': 'real-estate-portal',
      'project-16': 'healthcare-management',
      'project-17': 'fitness-tracking-app',
      'project-18': 'music-streaming-service',
      'project-19': 'online-marketplace',
      'project-20': 'project-management-tool'
    };
    return projectMapping[projectId] || projectId.replace('project-', 'project-');
  };

  const handleNewChat = () => {
    // Navigate to new chat creation or main chat interface for this project
    // For now, we'll check if there's a newchat route or navigate to the general chat interface
    console.log('Create new chat for project:', projectId);
    // In production, this might navigate to a new chat interface:
    // router.push(`/${tenant}/${projectId}/newchat`);
  };

  const handleBackToProjects = () => router.push(`/${tenant}/projects`);

  // Mock project data - in production, this would come from an API
  const getProjectData = (projectId: string) => {
    const projectData: { [key: string]: { name: string; description: string; techStack: string[] } } = {
      'project-1': {
        name: 'Mobile Banking App',
        description: 'React Native application with biometric authentication, real-time transactions, and advanced security features',
        techStack: ['React', 'Node.js']
      },
      'project-2': {
        name: 'E-Commerce Platform',
        description: 'Full-stack e-commerce solution with React, Node.js, and MongoDB for a modern online store',
        techStack: ['React Native', 'Node.js']
      },
      'project-3': {
        name: 'Analytics Dashboard',
        description: 'Data visualization platform with real-time metrics, custom charts using D3.js, and advanced filtering capabilities',
        techStack: ['Vue.js', 'D3.js']
      },
      'project-4': {
        name: 'Social Media App',
        description: 'Social platform with real-time messaging and content sharing',
        techStack: ['React', 'Socket.io']
      },
      'project-5': {
        name: 'Task Management Tool',
        description: 'Kanban-style task management with drag and drop functionality',
        techStack: ['Angular', 'Express']
      },
      'project-6': {
        name: 'Weather App Widget',
        description: 'Weather forecast widget with geolocation and animations',
        techStack: ['React', 'OpenWeather API']
      },
      'project-7': {
        name: 'Chat Application',
        description: 'Real-time chat application with WebSocket connections',
        techStack: ['Next.js', 'WebSocket']
      },
      'project-8': {
        name: 'Portfolio Website',
        description: 'Personal portfolio with modern animations and dark mode',
        techStack: ['React', 'Framer Motion']
      },
      'project-9': {
        name: 'Blog CMS System',
        description: 'Content management system with rich text editor',
        techStack: ['WordPress', 'PHP']
      },
      'project-10': {
        name: 'Inventory Management',
        description: 'Inventory tracking system with barcode scanning',
        techStack: ['React', 'MongoDB']
      },
      'project-11': {
        name: 'Customer Support Bot',
        description: 'AI-powered customer support chatbot integration',
        techStack: ['Python', 'TensorFlow']
      },
      'project-12': {
        name: 'Video Streaming App',
        description: 'Video streaming platform with adaptive bitrate streaming',
        techStack: ['React', 'WebRTC']
      },
      'project-13': {
        name: 'E-Learning Platform',
        description: 'Online learning platform with video courses and progress tracking',
        techStack: ['Vue.js', 'Laravel']
      },
      'project-14': {
        name: 'Food Delivery App',
        description: 'Food delivery app with real-time order tracking and payment integration',
        techStack: ['React Native', 'Firebase']
      },
      'project-15': {
        name: 'Real Estate Portal',
        description: 'Real estate portal with property listings and virtual tours',
        techStack: ['React', 'PostgreSQL']
      },
      'project-16': {
        name: 'Healthcare Management',
        description: 'Healthcare management system with patient records and appointment scheduling',
        techStack: ['Angular', 'Django']
      },
      'project-17': {
        name: 'Fitness Tracking App',
        description: 'Fitness tracking app with workout plans and progress monitoring',
        techStack: ['Flutter', 'Firebase']
      },
      'project-18': {
        name: 'Music Streaming Service',
        description: 'Music streaming service with personalized recommendations',
        techStack: ['React', 'Spotify API']
      },
      'project-19': {
        name: 'Online Marketplace',
        description: 'Online marketplace with seller dashboard and payment processing',
        techStack: ['Next.js', 'Stripe']
      },
      'project-20': {
        name: 'Project Management Tool',
        description: 'Project management tool with team collaboration features',
        techStack: ['React', 'GraphQL']
      }
    };
    
    return projectData[projectId] || {
      name: `Project ${projectId.replace('project-', '')}`,
      description: 'Project description not available',
      techStack: []
    };
  };

  if (!projectId || !tenant) return null;

  const projectData = getProjectData(projectId);

  return (
    <ProjectChatHistory
      projectId={projectId}
      projectName={projectData.name}
      projectDescription={projectData.description}
      projectTechStack={projectData.techStack}
      onChatClick={handleChatClick}
      onNewChat={handleNewChat}
      onBackToProjects={handleBackToProjects}
    />
  );
}
