import { ReactNode } from 'react';
import { TenantProvider } from '@/components/tenant/tenant-provider';
import { SidebarProvider } from '@/components/sidebar';
import { SidebarLayout } from '@/components/sidebar/sidebar-layout';

interface TenantLayoutProps {
  children: ReactNode;
  params: Promise<{
    tenant: string;
  }>;
}

export default async function TenantLayout({
  children,
  params,
}: TenantLayoutProps) {
  const { tenant } = await params;

  return (
    <TenantProvider tenantSlug={tenant}>
      <SidebarProvider>
        <div className="min-h-screen bg-background">
          <div className="tenant-context" data-tenant={tenant}>
            <SidebarLayout>
              {children}
            </SidebarLayout>
          </div>
        </div>
      </SidebarProvider>
    </TenantProvider>
  );
}
