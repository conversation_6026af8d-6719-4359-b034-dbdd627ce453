'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Plus, ArrowUp, Square } from 'lucide-react';

type AttachedFile = File & { previewUrl?: string };

type ChatInputProps = {
  value: string;
  onChange: (value: string) => void;
  onSend?: (value: string, files: File[]) => void;
  disabled?: boolean;
  isLoading?: boolean;
  maxLength?: number;
  onFilesChange?: (count: number) => void;
  // New: layout variants
  variant?: 'landing' | 'inline';
  // New: size scale for inline usage
  size?: 'md' | 'sm';
  className?: string;
};

export function ChatInput({
  value,
  onChange,
  onSend,
  disabled = false,
  isLoading = false,
  maxLength = 8000,
  onFilesChange,
  variant = 'landing',
  size = 'md',
  className,
}: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [files, setFiles] = useState<AttachedFile[]>([]);
  
  // Apply size-specific styling
  const sizeClasses = size === 'sm' ? 'text-sm' : 'text-base';

  const remaining = useMemo(() => Math.max(0, maxLength - value.length), [value.length, maxLength]);
  const formatSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${Math.round(bytes / 102.4) / 10} KB`;
    return `${Math.round(bytes / 104857.6) / 10} MB`;
  };
  const fileExt = (name: string) => (name.includes('.') ? name.split('.').pop() || '' : '').toUpperCase();

  useEffect(() => {
    return () => {
      files.forEach((f) => {
        if (f.previewUrl) URL.revokeObjectURL(f.previewUrl);
      });
    };
  }, [files]);

  // Inform parent about file count to adjust surrounding layout (avoid overlap)
  useEffect(() => {
    onFilesChange?.(files.length);
  }, [files.length, onFilesChange]);

  // Ensure caret stays visible when typing near the bottom
  useEffect(() => {
    const el = textareaRef.current;
    if (!el) return;
    const { selectionStart } = el;
    // trigger scroll into view for caret line
    const lineHeight = 24;
    const caretLine = Math.floor((selectionStart || 0) / 80); // rough estimate
    const targetScroll = caretLine * lineHeight - (el.clientHeight - lineHeight * 2);
    if (targetScroll > el.scrollTop) el.scrollTop = targetScroll;
  }, [value]);

  const handleSend = () => {
    if (disabled || isLoading) return;
    if (!value.trim() && files.length === 0) return;
    onSend?.(value, files);
  };

  const addFiles = (incoming: FileList | File[]) => {
    const next: AttachedFile[] = [];
    Array.from(incoming).forEach((file) => {
      const withPreview = file as AttachedFile;
      if (withPreview.type.startsWith('image/')) {
        withPreview.previewUrl = URL.createObjectURL(withPreview);
      }
      next.push(withPreview);
    });
    setFiles((prev) => [...prev, ...next].slice(0, 10));
  };

  // Inline compact composer (used inside chat view)
  if (variant === 'inline') {
    // Note: size parameter is not used in inline variant as height is dynamic
    return (
      <div
        className={className}
        onDragOver={(e) => {
          e.preventDefault();
        }}
        onDrop={(e) => {
          e.preventDefault();
          if (e.dataTransfer?.files?.length) addFiles(e.dataTransfer.files);
        }}
      >
        <div className="w-full max-w-none px-3 bg-white rounded-[10px] shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.08)] outline outline-offset-[-1px] outline-[#f26a1b]/50 flex flex-col relative">
          {/* Textarea container with flexible height */}
          <div className="py-2">
            <textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => {
                const next = e.target.value.slice(0, maxLength);
                onChange(next);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSend();
                }
              }}
              onPaste={(e) => {
                if (e.clipboardData?.files?.length) addFiles(e.clipboardData.files);
              }}
              placeholder="How can i help you"
              className={`w-full pr-12 text-[#191616] ${sizeClasses} font-normal font-['Inter'] leading-6 bg-transparent outline-none resize-none overflow-y-auto`}
              style={{
                minHeight: '24px', // 1 line minimum
                maxHeight: '144px', // 6 lines maximum (24px * 6)
                height: 'auto',
              }}
              rows={1}
              disabled={disabled || isLoading}
              onInput={(e) => {
                // Auto-resize textarea
                const target = e.target as HTMLTextAreaElement;
                target.style.height = 'auto';
                target.style.height = `${Math.min(target.scrollHeight, 144)}px`;
              }}
            />
          </div>

          {/* Bottom area: actions row - always fixed at bottom */}
          <div className="flex justify-between items-center px-0 pb-2">
            <div className="flex gap-2 items-center">
              <button
                type="button"
                disabled={disabled || isLoading}
                onClick={() => fileInputRef.current?.click()}
                className="size-6 relative bg-white rounded-3xl outline outline-offset-[-1px] outline-gray-200 overflow-hidden disabled:opacity-50 cursor-pointer"
                aria-label="Attach files"
              >
                <div className="size-4 left-[4px] top-[4px] absolute overflow-hidden">
                  <Plus className="w-[9.33px] h-[9.33px] left-[3.33px] top-[3.33px] absolute text-gray-600" strokeWidth={2} />
                </div>
              </button>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={(e) => {
                  if (e.target.files?.length) addFiles(e.target.files);
                  // Reset to allow selecting the same file again
                  if (fileInputRef.current) fileInputRef.current.value = '';
                }}
              />
            </div>
            <div className="flex gap-2 items-center">
              <span className="text-[11px] text-[#191616]/60 select-none">{remaining}</span>
              {isLoading ? (
                <button
                  type="button"
                  onClick={() => { /* noop */ }}
                  className={`overflow-hidden relative bg-gray-500 rounded-3xl size-6`}
                  aria-label="Stop"
                  disabled
                >
                  <div className="size-4 left-[4px] top-[4px] absolute overflow-hidden">
                    <Square className="w-[9.33px] h-3 left-[3.33px] top-[2px] absolute text-white" strokeWidth={2} />
                  </div>
                </button>
              ) : (
                <button
                  type="button"
                  onClick={handleSend}
                  className={`size-6 relative rounded-3xl overflow-hidden ${value.trim() || files.length ? 'bg-[#f26a1b]' : 'bg-gray-300'} disabled:opacity-50 cursor-pointer`}
                  aria-label="Send"
                  disabled={disabled || (!value.trim() && files.length === 0)}
                >
                  <div className="size-4 left-[4px] top-[4px] absolute overflow-hidden">
                    <ArrowUp className="w-[9.33px] h-3 left-[3.33px] top-[2px] absolute text-white" strokeWidth={2} />
                  </div>
                </button>
              )}
            </div>
          </div>

          {/* File preview section */}
          {files.length > 0 && (
            <div className="px-3 pb-2">
              <div className="mb-2 h-px bg-black/5" />
              <div className="flex flex-wrap gap-2 min-h-[58px]">
                {files.map((file, idx) => (
                  <div key={`${file.name}-${idx}`} className="relative shrink-0 w-[82px]">
                    <div className="w-[82px] h-[62px] bg-[#F7F8F9] rounded-md overflow-hidden flex items-center justify-center">
                      {file.previewUrl ? (
                        <img src={file.previewUrl} alt={file.name} className="object-cover w-full h-full" />
                      ) : (
                        <div className="flex flex-col items-center justify-center text-[#191616]/70">
                          <span className="text-[11px]">{fileExt(file.name) || 'FILE'}</span>
                          <span className="text-[10px]">{formatSize(file.size)}</span>
                        </div>
                      )}
                    </div>
                    <div className="mt-1 text-[11px] text-[#191616] truncate" title={file.name}>{file.name}</div>
                    <button
                      type="button"
                      aria-label={`Remove ${file.name}`}
                      className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-white outline outline-offset-[-1px] outline-gray-200 text-[#191616]/70 hover:text-[#191616] flex items-center justify-center cursor-pointer"
                      onClick={() => setFiles((prev) => prev.filter((_, i) => i !== idx))}
                    >
                      <span className="leading-none">×</span>
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  aria-label="Clear all attachments"
                  className="shrink-0 h-[62px] px-2 rounded-md bg-[#F7F8F9] text-[11px] text-[#191616]/70 hover:text-[#191616]"
                  onClick={() => setFiles([])}
                >
                  Clear all
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Main chat input container - preserves your style/positioning */}
      <div 
        className="absolute"
        style={{
          left: '50%',
          top: '238px',
          transform: 'translateX(-50%)'
        }}
        onDragOver={(e) => {
          e.preventDefault();
        }}
        onDrop={(e) => {
          e.preventDefault();
          if (e.dataTransfer?.files?.length) addFiles(e.dataTransfer.files);
        }}
      >
        <div className="w-[616px] px-3 pt-2.5 pb-9 bg-white rounded-[10px] shadow-[0px_2px_4px_-1px_rgba(0,0,0,0.08)] outline outline-offset-[-1px] outline-[#f26a1b]/50 inline-flex flex-col justify-start items-center overflow-hidden relative" style={{ height: files.length > 0 ? '236px' : '156px' }}>
          <div className="inline-flex relative flex-1 justify-between items-start self-stretch w-full">
            {/* {(!value && files.length === 0) && (
              <div className="flex-1 opacity-70 justify-start text-[#191616] text-sm font-normal font-['Inter'] leading-[23.10px] select-none">How can i help you</div>
            )} */}
            <textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => {
                const next = e.target.value.slice(0, maxLength);
                onChange(next);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSend();
                }
              }}
              onPaste={(e) => {
                if (e.clipboardData?.files?.length) addFiles(e.clipboardData.files);
              }}
              placeholder="How can i help you"
              className={`absolute left-0 w-full pr-10 pb-2 text-[#191616] ${sizeClasses} font-normal font-['Inter'] leading-[23.10px] bg-transparent outline-none overflow-auto resize-none`}
              style={{ top: 0, bottom: files.length > 0 ? 92 : 1 }}
              disabled={disabled || isLoading}
            />
          </div>

          {/* Bottom area: actions row above preview tiles */}
          <div className="absolute bottom-2 right-3 left-3">
            <div className="flex justify-between items-center">
              <div className="flex gap-2 items-center">
                <button
                  type="button"
                  disabled={disabled || isLoading}
                  onClick={() => fileInputRef.current?.click()}
                  className="size-6 relative bg-white rounded-3xl outline outline-offset-[-1px] outline-gray-200 overflow-hidden disabled:opacity-50 cursor-pointer"
                  aria-label="Attach files"
                >
                  <div className="size-4 left-[4px] top-[4px] absolute overflow-hidden">
                    <Plus className="w-[9.33px] h-[9.33px] left-[3.33px] top-[3.33px] absolute text-gray-600" strokeWidth={2} />
                  </div>
                </button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files?.length) addFiles(e.target.files);
                    // Reset to allow selecting the same file again
                    if (fileInputRef.current) fileInputRef.current.value = '';
                  }}
                />
              </div>
              <div className="flex gap-2 items-center">
                <span className="text-[11px] text-[#191616]/60 select-none">{remaining}</span>
                {isLoading ? (
                  <button
                    type="button"
                    onClick={() => {/* noop: parent can control isLoading to stop */}}
                    className={`overflow-hidden relative bg-gray-500 rounded-3xl size-6`}
                    aria-label="Stop"
                    disabled
                  >
                    <div className="size-4 left-[4px] top-[4px] absolute overflow-hidden">
                      <Square className="w-[9.33px] h-3 left-[3.33px] top-[2px] absolute text-white" strokeWidth={2} />
                    </div>
                  </button>
                ) : (
                  <button
                    type="button"
                    onClick={handleSend}
                    className={`size-6 relative rounded-3xl overflow-hidden ${value.trim() || files.length ? 'bg-[#f26a1b]' : 'bg-gray-300'} disabled:opacity-50 cursor-pointer`}
                    aria-label="Send"
                    disabled={disabled || (!value.trim() && files.length === 0)}
                  >
                    <div className="size-4 left-[4px] top-[4px] absolute overflow-hidden">
                      <ArrowUp className="w-[9.33px] h-3 left-[3.33px] top-[2px] absolute text-white" strokeWidth={2} />
                    </div>
                  </button>
                )}
              </div>
            </div>

            {files.length > 0 && (
              <div className="mt-2" style={{ minHeight: 80 }}>
                <div className="mb-2 h-px bg-black/5" />
                <div className="flex flex-wrap gap-2 min-h-[58px]">
                  {files.map((file, idx) => (
                    <div key={`${file.name}-${idx}`} className="relative shrink-0 w-[82px]">
                      <div className="w-[82px] h-[62px] bg-[#F7F8F9] rounded-md overflow-hidden flex items-center justify-center">
                        {file.previewUrl ? (
                          <img src={file.previewUrl} alt={file.name} className="object-cover w-full h-full" />
                        ) : (
                          <div className="flex flex-col items-center justify-center text-[#191616]/70">
                            <span className="text-[11px]">{fileExt(file.name) || 'FILE'}</span>
                            <span className="text-[10px]">{formatSize(file.size)}</span>
                          </div>
                        )}
                      </div>
                      <div className="mt-1 text-[11px] text-[#191616] truncate" title={file.name}>{file.name}</div>
                      <button
                        type="button"
                        aria-label={`Remove ${file.name}`}
                        className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-white outline outline-offset-[-1px] outline-gray-200 text-[#191616]/70 hover:text-[#191616] flex items-center justify-center cursor-pointer"
                        onClick={() => setFiles((prev) => prev.filter((_, i) => i !== idx))}
                      >
                        <span className="leading-none">×</span>
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    aria-label="Clear all attachments"
                    className="shrink-0 h-[62px] px-2 rounded-md bg-[#F7F8F9] text-[11px] text-[#191616]/70 hover:text-[#191616]"
                    onClick={() => setFiles([])}
                  >
                    Clear all
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile responsive adjustments */}
      <style jsx>{`
        @media (max-width: 768px) {
          .absolute[style*="width: 616px"] {
            width: calc(100vw - 40px) !important;
            left: 20px !important;
            transform: none !important;
          }
          
          .absolute[style*="width: 592px"] {
            width: calc(100vw - 60px) !important;
            left: 30px !important;
            transform: none !important;
          }
          
          .absolute[style*="width: 580px"] {
            width: calc(100vw - 80px) !important;
            left: 40px !important;
            transform: none !important;
          }
        }
        
        @media (max-width: 480px) {
          .absolute[style*="top: 238px"] {
            top: 160px !important;
          }
        }
      `}</style>
    </>
  );
}

export default ChatInput;


