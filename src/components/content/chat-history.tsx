'use client';

import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { MoreVertical, Loader2, Edit3, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// Types
interface ChatItem {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  emoji: string;
  createdAt: Date;
  messageCount: number;
}

interface ChatHistoryProps {
  onChatClick?: (chatId: string) => void;
  onNewChat?: () => void;
  className?: string;
}

// Mock data - In production, this would come from an API
const generateMockChats = (): ChatItem[] => {
  const emojis = ['💸', '🛍', '🚀', '💡', '🎨', '📱', '🌟', '🔥', '⚡', '🎯', '📊', '🛠', '🎭', '🌈', '🎪'];
  const titles = [
    'Product Card.tsx',
    'Mobile App Architecture Design',
    'Mobile Banking App',
    'E-commerce Dashboard',
    'React Hook Form Validation',
    'API Integration Guide',
    'Database Schema Planning',
    'UI Component Library',
    'Authentication System',
    'Payment Gateway Setup',
    'Social Media App',
    'Task Management Tool',
    'Weather App Widget',
    'Chat Application',
    'Portfolio Website',
    'Blog CMS System',
    'Inventory Management',
    'Customer Support Bot',
    'Analytics Dashboard',
    'Video Streaming App'
  ];
  
  const descriptions = [
    'Refining product card react component with advanced animations',
    'Full-stack e-commerce solution with React, Node.js, and MongoDB for a modern online store',
    'React Native application with biometric authentication, real-time transactions, and advanced security features',
    'Building responsive dashboard with TypeScript and Next.js',
    'Implementing form validation with Zod and React Hook Form',
    'Setting up REST API endpoints with proper error handling',
    'Designing scalable database architecture for microservices',
    'Creating reusable UI components with Storybook documentation',
    'JWT-based authentication with refresh token rotation',
    'Integrating Stripe payment processing with webhook handling',
    'Social platform with real-time messaging and content sharing',
    'Kanban-style task management with drag and drop functionality',
    'Weather forecast widget with geolocation and animations',
    'Real-time chat application with WebSocket connections',
    'Personal portfolio with modern animations and dark mode',
    'Content management system with rich text editor',
    'Inventory tracking system with barcode scanning',
    'AI-powered customer support chatbot integration',
    'Business analytics dashboard with interactive charts',
    'Video streaming platform with adaptive bitrate streaming'
  ];

  return Array.from({ length: 50 }, (_, index) => ({
    id: (index + 1).toString(), // Clean numeric IDs: "1", "2", "3", etc.
    title: titles[index % titles.length],
    description: descriptions[index % descriptions.length],
    timestamp: index < 5 ? 'Yesterday' : `Updated ${Math.floor(Math.random() * 30) + 1} days ago`,
    emoji: emojis[index % emojis.length],
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    messageCount: Math.floor(Math.random() * 50) + 1
  }));
};

export function ChatHistory({ onChatClick, onNewChat, className = '' }: ChatHistoryProps) {
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [visibleCount, setVisibleCount] = useState(6);
  const [isLoading, setIsLoading] = useState(false);
  const [allChats] = useState<ChatItem[]>(generateMockChats());
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Memoized filtered chats based on search query
  const filteredChats = useMemo(() => {
    if (!searchQuery.trim()) return allChats;
    
    const query = searchQuery.toLowerCase();
    return allChats.filter(chat => 
      chat.title.toLowerCase().includes(query) ||
      chat.description.toLowerCase().includes(query)
    );
  }, [allChats, searchQuery]);

  // Visible chats based on pagination
  const visibleChats = useMemo(() => 
    filteredChats.slice(0, visibleCount),
    [filteredChats, visibleCount]
  );

  const hasMoreChats = filteredChats.length > visibleCount;

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenMenuId(null);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Event handlers
  const handleChatClick = useCallback((chatId: string) => {
    onChatClick?.(chatId);
  }, [onChatClick]);

  const handleNewChat = useCallback(() => {
    onNewChat?.();
  }, [onNewChat]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setVisibleCount(6); // Reset pagination when searching
  }, []);

  const handleShowMore = useCallback(async () => {
    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    setVisibleCount(prev => prev + 6);
    setIsLoading(false);
  }, []);

  const handleMenuClick = useCallback((chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(openMenuId === chatId ? null : chatId);
  }, [openMenuId]);

  const handleRename = useCallback((chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle rename logic here
    console.log('Rename chat:', chatId);
    // In production, you might show a rename modal or inline edit
  }, []);

  const handleDelete = useCallback((chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle delete logic here
    console.log('Delete chat:', chatId);
    // In production, you might show a confirmation dialog
  }, []);

  return (
    <div className={`flex relative flex-col h-screen bg-white ${className}`}>
      {/* Decorative background element */}
      <div className="absolute top-0 right-0 w-[446px] h-[302px] rounded-full bg-[#F26A1B] opacity-40 blur-[240px] overflow-hidden pointer-events-none" />
      
      {/* Header - Fixed */}
      <div className="relative flex-shrink-0 border-b border-gray-100">
        <div className="max-w-[1380px] mx-auto flex items-center justify-between px-4 sm:px-8 py-6">
          <h1 className="text-xl sm:text-2xl font-semibold text-[#231616] leading-[150%]">
            Your chat history
          </h1>
          
          <div className="flex gap-2 items-center sm:gap-4">
            {/* Search Input */}
            <div className="relative">
              <Input
                value={searchQuery}
                onChange={handleSearchChange}
                placeholder="Search your chats..."
                className="w-48 sm:w-64 h-[38px] bg-[#F8F9FA] border-[#E5E5E2] rounded-lg px-4 text-sm placeholder:text-[#231616] placeholder:opacity-70 focus:border-[#F26A1B] focus:ring-[#F26A1B] transition-colors"
                aria-label="Search chat history"
              />
            </div>
            
            {/* New Chat Button */}
            <Button 
              onClick={handleNewChat}
              className="bg-[#F26A1B] hover:bg-[#E15E0D] text-white h-[38px] px-3 sm:px-4 rounded-md font-medium text-sm transition-colors"
              aria-label="Start new chat"
            >
              <span className="hidden sm:inline">New Chat</span>
              <span className="sm:hidden">New</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Search Results Info - Fixed */}
      {searchQuery && (
        <div className="relative flex-shrink-0 border-b border-gray-50">
          <div className="max-w-[1380px] mx-auto px-4 sm:px-8 py-3">
            <p className="text-sm text-[#231616] opacity-70">
              {filteredChats.length === 0 
                ? `No chats found for "${searchQuery}"` 
                : `${filteredChats.length} chat${filteredChats.length === 1 ? '' : 's'} found`
              }
            </p>
          </div>
        </div>
      )}

      {/* Chat List - Scrollable */}
      <div className="overflow-y-auto flex-1">
        <div className="max-w-[1380px] mx-auto">
          {visibleChats.length > 0 ? (
            <div className="px-4 py-6 sm:px-8">
              <div className="divide-y divide-[#E9E9E9]">
                {visibleChats.map((chat) => (
                  <div 
                    key={chat.id}
                    className="flex gap-4 items-start px-4 py-5 -mx-4 transition-all duration-200 cursor-pointer hover:bg-gray-50 group"
                    onClick={() => handleChatClick(chat.id)}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleChatClick(chat.id);
                      }
                    }}
                    aria-label={`Open chat: ${chat.title}`}
                  >
                    {/* Emoji Icon */}
                    <div className="flex-shrink-0 w-12 h-12 bg-[#F7F8F9] rounded-lg flex items-center justify-center group-hover:bg-[#F26A1B]/8 transition-all duration-200">
                      <span className="text-lg leading-[150%]">{chat.emoji}</span>
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      {/* Title and Menu */}
                      <div className="flex justify-between items-start mb-1">
                        <h3 className="text-base font-medium text-[#231616] leading-[164.99%] group-hover:text-[#F26A1B] transition-colors truncate pr-2">
                          {chat.title}
                        </h3>
                        
                        {/* Menu Button */}
                        <div className="relative" ref={openMenuId === chat.id ? menuRef : null}>
                          <button 
                            onClick={(e) => handleMenuClick(chat.id, e)}
                            className="flex-shrink-0 w-[32px] h-[32px] bg-white/90 backdrop-blur-sm rounded-lg flex items-center justify-center hover:bg-white hover:shadow-sm opacity-0 group-hover:opacity-100 transition-all duration-200 cursor-pointer border border-gray-100/50"
                            aria-label={`More options for ${chat.title}`}
                          >
                            <MoreVertical className="w-4 h-4 text-[#484546]" />
                          </button>
                          
                          {/* Dropdown Menu */}
                          {openMenuId === chat.id && (
                            <div className="absolute right-0 top-10 z-50 py-2 w-44 bg-white rounded-xl border border-gray-200 shadow-lg duration-100 animate-in fade-in-0 zoom-in-95">
                              <button
                                onClick={(e) => handleRename(chat.id, e)}
                                className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-[#231616] hover:bg-gray-50 transition-colors cursor-pointer"
                              >
                                <Edit3 className="w-4 h-4" />
                                Rename
                              </button>
                              <button
                                onClick={(e) => handleDelete(chat.id, e)}
                                className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-red-600 hover:bg-red-50 transition-colors cursor-pointer"
                              >
                                <Trash2 className="w-4 h-4" />
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Timestamp */}
                      <div className="mb-2">
                        <span className="text-sm text-[#231616] opacity-80 leading-[164.99%]">
                          {chat.timestamp}
                        </span>
                      </div>
                      
                      {/* Description */}
                      <p className="text-sm text-[#231616] leading-[164.99%] line-clamp-2">
                        {chat.description}
                      </p>
                    </div>
                  </div>
                ))}
                
                {/* Show More Button */}
                {hasMoreChats && (
                  <div className="flex justify-center pt-8 pb-6">
                    <Button
                      onClick={handleShowMore}
                      disabled={isLoading}
                      variant="outline"
                      className="border-[#E5E5E2] text-[#231616] hover:border-[#F26A1B] hover:text-[#F26A1B] disabled:opacity-50 min-w-[200px] cursor-pointer disabled:cursor-not-allowed"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 w-4 h-4 animate-spin" />
                          Loading more chats...
                        </>
                      ) : (
                        'Show More'
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Empty State */
            <div className="flex flex-1 justify-center items-center py-20">
              <div className="px-4 mx-auto max-w-md text-center">
                <div className="flex justify-center items-center mx-auto mb-4 w-16 h-16 bg-gray-100 rounded-full">
                  <span className="text-2xl">💬</span>
                </div>
                <h3 className="text-lg font-medium text-[#231616] mb-2">
                  {searchQuery ? 'No chats found' : 'No chat history yet'}
                </h3>
                <p className="text-[#231616] opacity-80 mb-6">
                  {searchQuery 
                    ? `Try adjusting your search term "${searchQuery}"` 
                    : 'Start a conversation to see your chat history here'
                  }
                </p>
                {!searchQuery && (
                  <Button 
                    onClick={handleNewChat}
                    className="bg-[#F26A1B] hover:bg-[#E15E0D] text-white"
                  >
                    Start New Chat
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}