'use client';

import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { MoreVertical, Loader2, Edit3, Trash2, FolderOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// Types
interface ProjectItem {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  emoji: string;
  createdAt: Date;
  chatCount: number;
  techStack: string[];
}

interface ProjectsProps {
  onProjectClick?: (projectId: string) => void;
  onNewProject?: () => void;
  className?: string;
}

// Mock data - In production, this would come from an API
const generateMockProjects = (): ProjectItem[] => {
  const emojis = ['💸', '🛍', '🚀', '💡', '🎨', '📱', '🌟', '🔥', '⚡', '🎯', '📊', '🛠', '🎭', '🌈', '🎪'];
  const titles = [
    'Mobile Banking App',
    'E-Commerce Platform',
    'Analytics Dashboard',
    'Social Media App',
    'Task Management Tool',
    'Weather App Widget',
    'Chat Application',
    'Portfolio Website',
    'Blog CMS System',
    'Inventory Management',
    'Customer Support Bot',
    'Video Streaming App',
    'E-Learning Platform',
    'Food Delivery App',
    'Real Estate Portal',
    'Healthcare Management',
    'Fitness Tracking App',
    'Music Streaming Service',
    'Online Marketplace',
    'Project Management Tool'
  ];
  
  const descriptions = [
    'React Native application with biometric authentication, real-time transactions, and advanced security features',
    'Full-stack e-commerce solution with React, Node.js, and MongoDB for a modern online store',
    'Data visualization platform with real-time metrics, custom charts using D3.js, and advanced filtering capabilities',
    'Social platform with real-time messaging and content sharing',
    'Kanban-style task management with drag and drop functionality',
    'Weather forecast widget with geolocation and animations',
    'Real-time chat application with WebSocket connections',
    'Personal portfolio with modern animations and dark mode',
    'Content management system with rich text editor',
    'Inventory tracking system with barcode scanning',
    'AI-powered customer support chatbot integration',
    'Video streaming platform with adaptive bitrate streaming',
    'Online learning platform with video courses and progress tracking',
    'Food delivery app with real-time order tracking and payment integration',
    'Real estate portal with property listings and virtual tours',
    'Healthcare management system with patient records and appointment scheduling',
    'Fitness tracking app with workout plans and progress monitoring',
    'Music streaming service with personalized recommendations',
    'Online marketplace with seller dashboard and payment processing',
    'Project management tool with team collaboration features'
  ];

  const techStacks = [
    ['React', 'Node.js'],
    ['React Native', 'Node.js'],
    ['Vue.js', 'D3.js'],
    ['React', 'Socket.io'],
    ['Angular', 'Express'],
    ['React', 'OpenWeather API'],
    ['Next.js', 'WebSocket'],
    ['React', 'Framer Motion'],
    ['WordPress', 'PHP'],
    ['React', 'MongoDB'],
    ['Python', 'TensorFlow'],
    ['React', 'WebRTC'],
    ['Vue.js', 'Laravel'],
    ['React Native', 'Firebase'],
    ['React', 'PostgreSQL'],
    ['Angular', 'Django'],
    ['Flutter', 'Firebase'],
    ['React', 'Spotify API'],
    ['Next.js', 'Stripe'],
    ['React', 'GraphQL']
  ];
  
  return Array.from({ length: 20 }, (_, index) => ({
    id: `project-${index + 1}`,
    title: titles[index % titles.length],
    description: descriptions[index % descriptions.length],
    timestamp: index < 5 ? 'Yesterday' : `Updated ${Math.floor(Math.random() * 30) + 1} days ago`,
    emoji: emojis[index % emojis.length],
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    chatCount: Math.floor(Math.random() * 50) + 1,
    techStack: techStacks[index % techStacks.length]
  }));
};

export function Projects({ onProjectClick, onNewProject, className = '' }: ProjectsProps) {
  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [visibleCount, setVisibleCount] = useState(6);
  const [isLoading, setIsLoading] = useState(false);
  const [allProjects] = useState<ProjectItem[]>(generateMockProjects());
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Memoized filtered projects based on search query
  const filteredProjects = useMemo(() => {
    if (!searchQuery.trim()) return allProjects;
    
    const query = searchQuery.toLowerCase();
    return allProjects.filter(project => 
      project.title.toLowerCase().includes(query) ||
      project.description.toLowerCase().includes(query) ||
      project.techStack.some(tech => tech.toLowerCase().includes(query))
    );
  }, [allProjects, searchQuery]);

  // Visible projects based on pagination
  const visibleProjects = useMemo(() => 
    filteredProjects.slice(0, visibleCount),
    [filteredProjects, visibleCount]
  );

  const hasMoreProjects = filteredProjects.length > visibleCount;

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenMenuId(null);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Event handlers
  const handleProjectClick = useCallback((projectId: string) => {
    onProjectClick?.(projectId);
  }, [onProjectClick]);

  const handleNewProject = useCallback(() => {
    onNewProject?.();
  }, [onNewProject]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setVisibleCount(6); // Reset pagination when searching
  }, []);

  const handleShowMore = useCallback(async () => {
    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    setVisibleCount(prev => prev + 6);
    setIsLoading(false);
  }, []);

  const handleMenuClick = useCallback((projectId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(openMenuId === projectId ? null : projectId);
  }, [openMenuId]);

  const handleRename = useCallback((projectId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle rename logic here
    console.log('Rename project:', projectId);
    // In production, you might show a rename modal or inline edit
  }, []);

  const handleDelete = useCallback((projectId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenuId(null);
    // Handle delete logic here
    console.log('Delete project:', projectId);
    // In production, you might show a confirmation dialog
  }, []);

  return (
    <div className={`flex relative flex-col h-screen bg-white ${className}`}>
      {/* Decorative background element */}
      <div className="absolute top-0 right-0 w-[446px] h-[302px] rounded-full bg-[#F26A1B] opacity-40 blur-[240px] overflow-hidden pointer-events-none" />
      
      {/* Header - Fixed */}
      <div className="relative flex-shrink-0 border-b border-gray-100">
        <div className="max-w-[1380px] mx-auto flex items-center justify-between px-4 sm:px-8 py-6">
          <h1 className="text-xl sm:text-2xl font-semibold text-[#231616] leading-[150%]">
            All Projects
          </h1>
          
          <div className="flex gap-2 items-center sm:gap-4">
            {/* Search Input */}
            <div className="relative">
              <Input
                value={searchQuery}
                onChange={handleSearchChange}
                placeholder="Search projects..."
                className="w-48 sm:w-64 h-[38px] bg-[#F8F9FA] border-[#E5E5E2] rounded-lg px-4 text-sm placeholder:text-[#231616] placeholder:opacity-70 focus:border-[#F26A1B] focus:ring-[#F26A1B] transition-colors"
                aria-label="Search projects"
              />
            </div>
            
            {/* New Project Button */}
            <Button 
              onClick={handleNewProject}
              className="bg-[#F26A1B] hover:bg-[#E15E0D] text-white h-[38px] px-3 sm:px-4 rounded-md font-medium text-sm transition-colors"
              aria-label="Create new project"
            >
              <span className="hidden sm:inline">New Project</span>
              <span className="sm:hidden">New</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Search Results Info - Fixed */}
      {searchQuery && (
        <div className="relative flex-shrink-0 border-b border-gray-50">
          <div className="max-w-[1380px] mx-auto px-4 sm:px-8 py-3">
            <p className="text-sm text-[#231616] opacity-70">
              {filteredProjects.length === 0 
                ? `No projects found for "${searchQuery}"` 
                : `${filteredProjects.length} project${filteredProjects.length === 1 ? '' : 's'} found`
              }
            </p>
          </div>
        </div>
      )}

      {/* Projects List - Scrollable */}
      <div className="overflow-y-auto flex-1">
        <div className="max-w-[1380px] mx-auto">
          {visibleProjects.length > 0 ? (
            <div className="px-4 py-6 sm:px-8">
              <div className="divide-y divide-[#E9E9E9]">
                {visibleProjects.map((project) => (
                  <div 
                    key={project.id}
                    className="flex gap-4 items-start px-4 py-5 -mx-4 transition-all duration-200 cursor-pointer hover:bg-gray-50 group"
                    onClick={() => handleProjectClick(project.id)}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleProjectClick(project.id);
                      }
                    }}
                    aria-label={`Open project: ${project.title}`}
                  >
                    {/* Emoji Icon */}
                    <div className="flex-shrink-0 w-12 h-12 bg-[#F7F8F9] rounded-lg flex items-center justify-center group-hover:bg-[#F26A1B]/8 transition-all duration-200">
                      <span className="text-lg leading-[150%]">{project.emoji}</span>
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      {/* Title and Menu */}
                      <div className="flex justify-between items-start mb-1">
                        <h3 className="text-base font-medium text-[#231616] leading-[164.99%] group-hover:text-[#F26A1B] transition-colors truncate pr-2">
                          {project.title}
                        </h3>
                        
                        {/* Menu Button */}
                        <div className="relative" ref={openMenuId === project.id ? menuRef : null}>
                          <button 
                            onClick={(e) => handleMenuClick(project.id, e)}
                            className="flex-shrink-0 w-[32px] h-[32px] bg-white/90 backdrop-blur-sm rounded-lg flex items-center justify-center hover:bg-white hover:shadow-sm opacity-0 group-hover:opacity-100 transition-all duration-200 cursor-pointer border border-gray-100/50"
                            aria-label={`More options for ${project.title}`}
                          >
                            <MoreVertical className="w-4 h-4 text-[#484546]" />
                          </button>
                          
                          {/* Dropdown Menu */}
                          {openMenuId === project.id && (
                            <div className="absolute right-0 top-10 z-50 py-2 w-44 bg-white rounded-xl border border-gray-200 shadow-lg duration-100 animate-in fade-in-0 zoom-in-95">
                              <button
                                onClick={(e) => handleRename(project.id, e)}
                                className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-[#231616] hover:bg-gray-50 transition-colors cursor-pointer"
                              >
                                <Edit3 className="w-4 h-4" />
                                Rename
                              </button>
                              <button
                                onClick={(e) => handleDelete(project.id, e)}
                                className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-red-600 hover:bg-red-50 transition-colors cursor-pointer"
                              >
                                <Trash2 className="w-4 h-4" />
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Timestamp */}
                      <div className="mb-2">
                        <span className="text-sm text-[#231616] opacity-80 leading-[164.99%]">
                          {project.timestamp}
                        </span>
                      </div>
                      
                      {/* Description */}
                      <p className="text-sm text-[#231616] leading-[164.99%] line-clamp-2 mb-2">
                        {project.description}
                      </p>

                      {/* Tech Stack Tags */}
                      <div className="flex flex-wrap gap-1">
                        {project.techStack.map((tech, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded-md"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Show More Button */}
                {hasMoreProjects && (
                  <div className="flex justify-center pt-8 pb-6">
                    <Button
                      onClick={handleShowMore}
                      disabled={isLoading}
                      variant="outline"
                      className="border-[#E5E5E2] text-[#231616] hover:border-[#F26A1B] hover:text-[#F26A1B] disabled:opacity-50 min-w-[200px] cursor-pointer disabled:cursor-not-allowed"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 w-4 h-4 animate-spin" />
                          Loading more projects...
                        </>
                      ) : (
                        'Show More'
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Empty State */
            <div className="flex flex-1 justify-center items-center py-20">
              <div className="px-4 mx-auto max-w-md text-center">
                <div className="flex justify-center items-center mx-auto mb-4 w-16 h-16 bg-gray-100 rounded-full">
                  <FolderOpen className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-[#231616] mb-2">
                  {searchQuery ? 'No projects found' : 'No projects yet'}
                </h3>
                <p className="text-[#231616] opacity-80 mb-6">
                  {searchQuery 
                    ? `Try adjusting your search term "${searchQuery}"` 
                    : 'Create your first project to get started'
                  }
                </p>
                {!searchQuery && (
                  <Button 
                    onClick={handleNewProject}
                    className="bg-[#F26A1B] hover:bg-[#E15E0D] text-white"
                  >
                    Create New Project
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}