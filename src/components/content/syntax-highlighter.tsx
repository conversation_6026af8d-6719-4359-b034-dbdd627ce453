'use client';

import React from 'react';

interface SyntaxHighlighterProps {
  code: string;
  language: string;
  showLineNumbers?: boolean;
  className?: string;
}

// Simple syntax highlighting using regex patterns
const highlightCode = (code: string, language: string): string => {
  let highlighted = code;

  // HTML entity escape
  highlighted = highlighted
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');

  switch (language.toLowerCase()) {
    case 'javascript':
    case 'typescript':
    case 'jsx':
    case 'tsx':
      // Keywords
      highlighted = highlighted.replace(
        /\b(const|let|var|function|return|if|else|for|while|class|interface|type|import|export|from|default|async|await|try|catch|finally|throw|new|this|super|extends|implements|public|private|protected|static|readonly)\b/g,
        '<span class="text-blue-400">$1</span>'
      );
      
      // Strings
      highlighted = highlighted.replace(
        /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
        '<span class="text-green-400">$1$2$1</span>'
      );
      
      // Comments
      highlighted = highlighted.replace(
        /\/\/.*$/gm,
        '<span class="text-gray-400">$&</span>'
      );
      highlighted = highlighted.replace(
        /\/\*[\s\S]*?\*\//g,
        '<span class="text-gray-400">$&</span>'
      );
      
      // Numbers
      highlighted = highlighted.replace(
        /\b\d+\.?\d*\b/g,
        '<span class="text-purple-400">$&</span>'
      );
      break;

    case 'html':
      // HTML tags
      highlighted = highlighted.replace(
        /(&lt;\/?)([\w-]+)([^&]*?)(&gt;)/g,
        '<span class="text-blue-400">$1</span><span class="text-red-400">$2</span><span class="text-yellow-400">$3</span><span class="text-blue-400">$4</span>'
      );
      
      // Attributes
      highlighted = highlighted.replace(
        /(\w+)=(["'])(.*?)\2/g,
        '<span class="text-yellow-400">$1</span>=<span class="text-green-400">$2$3$2</span>'
      );
      break;

    case 'python':
      // Keywords
      highlighted = highlighted.replace(
        /\b(def|class|if|elif|else|for|while|try|except|finally|import|from|as|return|yield|break|continue|pass|with|lambda|and|or|not|in|is|None|True|False|self)\b/g,
        '<span class="text-blue-400">$1</span>'
      );
      
      // Strings
      highlighted = highlighted.replace(
        /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
        '<span class="text-green-400">$1$2$1</span>'
      );
      
      // Comments
      highlighted = highlighted.replace(
        /#.*$/gm,
        '<span class="text-gray-400">$&</span>'
      );
      
      // Numbers
      highlighted = highlighted.replace(
        /\b\d+\.?\d*\b/g,
        '<span class="text-purple-400">$&</span>'
      );
      break;

    case 'css':
      // Selectors
      highlighted = highlighted.replace(
        /([.#]?[\w-]+)(\s*{)/g,
        '<span class="text-yellow-400">$1</span>$2'
      );
      
      // Properties
      highlighted = highlighted.replace(
        /([\w-]+)(\s*:)/g,
        '<span class="text-blue-400">$1</span>$2'
      );
      
      // Values
      highlighted = highlighted.replace(
        /:\s*([^;}\n]+)/g,
        ': <span class="text-green-400">$1</span>'
      );
      break;

    default:
      // For unknown languages, just highlight strings and comments
      highlighted = highlighted.replace(
        /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
        '<span class="text-green-400">$1$2$1</span>'
      );
      highlighted = highlighted.replace(
        /\/\/.*$/gm,
        '<span class="text-gray-400">$&</span>'
      );
      break;
  }

  return highlighted;
};

export default function SyntaxHighlighter({ 
  code, 
  language, 
  showLineNumbers = true, 
  className = '' 
}: SyntaxHighlighterProps) {
  const lines = code.split('\n');
  const highlightedCode = highlightCode(code, language);
  const highlightedLines = highlightedCode.split('\n');

  return (
    <div className={`relative ${className}`}>
      <div className="flex bg-neutral-900 text-neutral-100 text-sm font-mono min-h-full">
        {showLineNumbers && (
          <div className="flex flex-col bg-neutral-800 text-neutral-400 text-right select-none border-r border-neutral-700 flex-shrink-0">
            {lines.map((_, index) => (
              <div
                key={index}
                className="px-3 py-0.5 min-h-[1.25rem] leading-5"
              >
                {index + 1}
              </div>
            ))}
          </div>
        )}
        <div className="flex-1 overflow-x-auto">
          <div className="p-4">
            {highlightedLines.map((line, index) => (
              <div
                key={index}
                className="min-h-[1.25rem] leading-5 whitespace-pre"
                dangerouslySetInnerHTML={{ __html: line || '&nbsp;' }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}