'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextValue {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  actualTheme: 'light' | 'dark';
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'kavia-theme',
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window === 'undefined') return defaultTheme;
    const saved = localStorage.getItem(storageKey) as Theme | null;
    return saved ?? defaultTheme;
  });
  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>(() => {
    if (typeof window === 'undefined') return 'light';
    const t =
      (localStorage.getItem(storageKey) as Theme | null) ?? defaultTheme;
    if (t === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'dark'
        : 'light';
    }
    return t;
  });

  useEffect(() => {
    const root = window.document.documentElement;
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const desiredIsDark =
      theme === 'system' ? mediaQuery.matches : theme === 'dark';
    const desiredTheme: 'light' | 'dark' = desiredIsDark ? 'dark' : 'light';
    setActualTheme(desiredTheme);
    const hasDark = root.classList.contains('dark');
    if (desiredIsDark && !hasDark) {
      root.classList.add('dark');
    } else if (!desiredIsDark && hasDark) {
      root.classList.remove('dark');
    }
  }, [theme]);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const applySystem = () => {
      const root = window.document.documentElement;
      const isDark = mediaQuery.matches;
      const desiredTheme: 'light' | 'dark' = isDark ? 'dark' : 'light';
      setActualTheme(desiredTheme);
      const hasDark = root.classList.contains('dark');
      if (isDark && !hasDark) {
        root.classList.add('dark');
      } else if (!isDark && hasDark) {
        root.classList.remove('dark');
      }
    };

    const handleChange = () => {
      if (theme === 'system') {
        applySystem();
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    if (theme === 'system') {
      applySystem();
    }
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  const handleSetTheme = (newTheme: Theme) => {
    localStorage.setItem(storageKey, newTheme);
    setTheme(newTheme);
  };

  const value: ThemeContextValue = {
    theme,
    setTheme: handleSetTheme,
    actualTheme,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
