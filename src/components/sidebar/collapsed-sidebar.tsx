'use client';

import React, { useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Plus, 
  MessageSquare, 
  Briefcase,
  PanelLeftOpen,
  User,
  LogOut,
  Settings,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { IconButton } from '@/components/ui/icon-button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useSidebar } from './sidebar-context';
import { useTenant } from '@/components/tenant/tenant-provider';
import { cn } from '@/lib/utils';

interface CollapsedSidebarProps {
  className?: string;
}

export function CollapsedSidebar({ className }: CollapsedSidebarProps) {
  const { state, toggle, setActiveItem } = useSidebar();
  const { tenantSlug } = useTenant();

  // Mock user data - replace with actual user context
  const user = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: '',
    initials: 'JD',
  };

  const handleLogout = useCallback(() => {
    console.log('Logout');
  }, []);

  const router = useRouter();

  const handleNewChatClick = useCallback(() => {
    setActiveItem('new-chat');
    router.push(`/${tenantSlug}/newchat`);
  }, [setActiveItem, router, tenantSlug]);

  const handleChatHistoryClick = useCallback(() => {
    setActiveItem('chat-history');
    router.push(`/${tenantSlug}/chats`);
  }, [setActiveItem, router, tenantSlug]);

  const handleProjectsClick = useCallback(() => {
    setActiveItem('projects');
    router.push(`/${tenantSlug}/projects`);
  }, [setActiveItem, router, tenantSlug]);

  const navItems = useMemo(() => [
    {
      id: 'new-chat',
      label: 'New Chat',
      icon: Plus,
      href: `/${tenantSlug}/project-1/chat`,
      isActive: state.activeItem === 'new-chat',
      onClick: handleNewChatClick,
    },
    {
      id: 'chat-history',
      label: 'Chat History',
      icon: MessageSquare,
      href: `/${tenantSlug}/project-1/chat`,
      isActive: state.activeItem === 'chat-history',
      onClick: handleChatHistoryClick,
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: Briefcase,
      href: `/${tenantSlug}/projects`,
      isActive: state.activeItem === 'projects',
      onClick: handleProjectsClick,
    },
  ], [tenantSlug, state.activeItem, handleNewChatClick, handleChatHistoryClick, handleProjectsClick]);

  return (
    <TooltipProvider>
      <div
        className={cn(
          'fixed left-0 top-0 z-50 h-full bg-white border-r border-gray-200',
          'flex flex-col items-center',
          'shadow-sm',
          className
        )}
        style={{ width: state.collapsedWidth }}
      >
        {/* Header with expand button */}
        <div className="flex flex-col items-center w-full pt-4 pb-4">
          <Tooltip>
            <TooltipTrigger asChild>
              <IconButton
                variant="ghost"
                onClick={toggle}
                className="w-8 h-8 rounded-xl hover:bg-gray-100"
                aria-label="Expand Sidebar"
                icon={<PanelLeftOpen className="w-4 h-4 text-gray-600" />}
              />
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>Expand Sidebar</p>
            </TooltipContent>
          </Tooltip>
        </div>

      {/* Navigation Items */}
      <div className="flex flex-col items-center flex-1 px-3 space-y-4">
        {navItems.map((item) => {
          const IconComponent = item.icon;
          
          return (
            <Tooltip key={item.id}>
              <TooltipTrigger asChild>
                <IconButton
                  variant="ghost"
                  onClick={item.onClick}
                  className={cn(
                    'w-8 h-8 rounded-xl transition-all duration-200 hover:scale-105 active:scale-95',
                    item.isActive 
                      ? 'bg-[#FFEFE4] text-[#F26A1B] shadow-sm' 
                      : 'hover:bg-gray-100 text-gray-600'
                  )}
                  aria-label={item.label}
                  icon={<IconComponent className="w-4 h-4" />}
                />
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{item.label}</p>
              </TooltipContent>
            </Tooltip>
          );
        })}
      </div>

      {/* User Avatar at Bottom */}
      <div className="flex flex-col items-center px-3 pb-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="w-8 h-8 p-0 rounded-xl hover:bg-gray-100"
            >
              <Avatar className="w-8 h-8">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="text-xs font-medium text-[#F26A1B] bg-[#FFEFE4]">
                  {user.initials}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" side="right" className="w-56">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium">{user.name}</p>
                <p className="text-xs text-muted-foreground">{user.email}</p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            <DropdownMenuItem>
              <User className="w-4 h-4 mr-2" />
              Profile
            </DropdownMenuItem>
            
            <DropdownMenuItem>
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem 
              onClick={handleLogout}
              className="text-red-600 focus:text-red-600"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      </div>
    </TooltipProvider>
  );
}

export default CollapsedSidebar;
