'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ScrollArea } from '@/components/ui/scroll-area';
import { SidebarSection } from './sidebar-section';
import { useSidebarSections } from './sidebar-context';
import { cn } from '@/lib/utils';

interface SidebarContentProps {
  className?: string;
}

export function SidebarContent({ className }: SidebarContentProps) {
  const { sections } = useSidebarSections();

  // Animation variants for sections
  const sectionVariants = {
    hidden: {
      opacity: 0,
      y: 10,
    },
    visible: {
      opacity: 1,
      y: 0,
    },
  };

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.05,
      },
    },
  };

  return (
    <div className={cn('flex-1 overflow-hidden', className)}>
      <ScrollArea className="h-full">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={containerVariants}
          className="p-2 space-y-1"
        >
          <AnimatePresence mode="popLayout">
            {sections.map((section) => (
              <motion.div
                key={section.id}
                variants={sectionVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                layout
              >
                <SidebarSection section={section} />
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Empty state */}
          {sections.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex flex-col items-center justify-center h-32 text-center text-muted-foreground"
            >
              <p className="text-sm">No items to display</p>
              <p className="text-xs mt-1">Start a new conversation to see recent chats</p>
            </motion.div>
          )}
        </motion.div>
      </ScrollArea>
    </div>
  );
}

export default SidebarContent;
