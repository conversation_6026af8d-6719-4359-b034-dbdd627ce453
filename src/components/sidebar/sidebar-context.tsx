'use client';

import React, { createContext, useContext, useReducer, ReactNode, useEffect, useMemo, useCallback } from 'react';
import { SidebarState, SidebarContextValue, SidebarSection, SidebarItem } from '@/types';

// Action types
type SidebarAction =
  | { type: 'TOGGLE' }
  | { type: 'EXPAND' }
  | { type: 'COLLAPSE' }
  | { type: 'OPEN' }
  | { type: 'CLOSE' }
  | { type: 'SET_WIDTH'; payload: number }
  | { type: 'SET_ACTIVE_ITEM'; payload: string }
  | { type: 'TOGGLE_PIN' }
  | { type: 'UPDATE_SECTION'; payload: { sectionId: string; updates: Partial<SidebarSection> } }
  | { type: 'ADD_ITEM'; payload: { sectionId: string; item: SidebarItem; index?: number } }
  | { type: 'REMOVE_ITEM'; payload: { sectionId: string; itemId: string } }
  | { type: 'SET_SECTIONS'; payload: SidebarSection[] }
  | { type: 'LOAD_STATE'; payload: Partial<SidebarState> };

// Initial state
const initialState: SidebarState = {
  isOpen: true,
  isExpanded: false,
  width: 280,
  collapsedWidth: 60,
  sections: [],
  activeItem: undefined,
  isPinned: false,
};

// Reducer
function sidebarReducer(state: SidebarState, action: SidebarAction): SidebarState {
  switch (action.type) {
    case 'TOGGLE':
      return { ...state, isExpanded: !state.isExpanded };
    
    case 'EXPAND':
      return { ...state, isExpanded: true };
    
    case 'COLLAPSE':
      return { ...state, isExpanded: false };
    
    case 'OPEN':
      return { ...state, isOpen: true };
    
    case 'CLOSE':
      return { ...state, isOpen: false };
    
    case 'SET_WIDTH':
      return { ...state, width: Math.max(200, Math.min(400, action.payload)) };
    
    case 'SET_ACTIVE_ITEM':
      return { ...state, activeItem: action.payload };
    
    case 'TOGGLE_PIN':
      return { ...state, isPinned: !state.isPinned };
    
    case 'UPDATE_SECTION':
      return {
        ...state,
        sections: state.sections.map(section =>
          section.id === action.payload.sectionId
            ? { ...section, ...action.payload.updates }
            : section
        ),
      };
    
    case 'ADD_ITEM':
      return {
        ...state,
        sections: state.sections.map(section => {
          if (section.id === action.payload.sectionId) {
            const items = [...section.items];
            const index = action.payload.index ?? items.length;
            items.splice(index, 0, action.payload.item);
            return { ...section, items };
          }
          return section;
        }),
      };
    
    case 'REMOVE_ITEM':
      return {
        ...state,
        sections: state.sections.map(section => {
          if (section.id === action.payload.sectionId) {
            return {
              ...section,
              items: section.items.filter(item => item.id !== action.payload.itemId),
            };
          }
          return section;
        }),
      };
    
    case 'SET_SECTIONS':
      return { ...state, sections: action.payload };
    
    case 'LOAD_STATE':
      return { ...state, ...action.payload };
    
    default:
      return state;
  }
}

// Context
const SidebarContext = createContext<SidebarContextValue | undefined>(undefined);

// Provider component
interface SidebarProviderProps {
  children: ReactNode;
  defaultSections?: SidebarSection[];
  defaultWidth?: number;
  defaultPinned?: boolean;
  persistState?: boolean;
}

export function SidebarProvider({
  children,
  defaultSections = [],
  defaultWidth = 280,
  defaultPinned = false,
  persistState = true,
}: SidebarProviderProps) {
  const [state, dispatch] = useReducer(sidebarReducer, {
    ...initialState,
    sections: defaultSections,
    width: defaultWidth,
    isPinned: defaultPinned,
  });

  // Load persisted state on mount
  useEffect(() => {
    if (persistState && typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem('kavia-sidebar-state');
        if (saved) {
          const parsedState = JSON.parse(saved);
          dispatch({ type: 'LOAD_STATE', payload: parsedState });
        }
      } catch (error) {
        console.warn('Failed to load sidebar state from localStorage:', error);
      }
    }
  }, [persistState]);

  // Persist state changes
  useEffect(() => {
    if (persistState && typeof window !== 'undefined') {
      try {
        localStorage.setItem('kavia-sidebar-state', JSON.stringify({
          isOpen: state.isOpen,
          isExpanded: state.isExpanded,
          width: state.width,
          isPinned: state.isPinned,
          activeItem: state.activeItem,
        }));
      } catch (error) {
        console.warn('Failed to save sidebar state to localStorage:', error);
      }
    }
  }, [state.isOpen, state.isExpanded, state.width, state.isPinned, state.activeItem, persistState]);

  // Memoized context functions
  const toggle = useCallback(() => dispatch({ type: 'TOGGLE' }), []);
  const expand = useCallback(() => dispatch({ type: 'EXPAND' }), []);
  const collapse = useCallback(() => dispatch({ type: 'COLLAPSE' }), []);
  const open = useCallback(() => dispatch({ type: 'OPEN' }), []);
  const close = useCallback(() => dispatch({ type: 'CLOSE' }), []);
  const setWidth = useCallback((width: number) => dispatch({ type: 'SET_WIDTH', payload: width }), []);
  const setActiveItem = useCallback((itemId: string) => dispatch({ type: 'SET_ACTIVE_ITEM', payload: itemId }), []);
  const togglePin = useCallback(() => dispatch({ type: 'TOGGLE_PIN' }), []);
  const updateSection = useCallback((sectionId: string, updates: Partial<SidebarSection>) =>
    dispatch({ type: 'UPDATE_SECTION', payload: { sectionId, updates } }), []);
  const addItem = useCallback((sectionId: string, item: SidebarItem, index?: number) =>
    dispatch({ type: 'ADD_ITEM', payload: { sectionId, item, index } }), []);
  const removeItem = useCallback((sectionId: string, itemId: string) =>
    dispatch({ type: 'REMOVE_ITEM', payload: { sectionId, itemId } }), []);

  // Context value
  const value: SidebarContextValue = useMemo(() => ({
    state,
    toggle,
    expand,
    collapse,
    open,
    close,
    setWidth,
    setActiveItem,
    togglePin,
    updateSection,
    addItem,
    removeItem,
  }), [state, toggle, expand, collapse, open, close, setWidth, setActiveItem, togglePin, updateSection, addItem, removeItem]);

  return (
    <SidebarContext.Provider value={value}>
      {children}
    </SidebarContext.Provider>
  );
}

// Hook to use sidebar context
export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}

// Hook for sidebar sections management
export function useSidebarSections() {
  const { state, updateSection, addItem, removeItem } = useSidebar();

  return {
    sections: state.sections,
    updateSection,
    addItem,
    removeItem,
    findSection: (sectionId: string) => 
      state.sections.find(section => section.id === sectionId),
    findItem: (itemId: string) => {
      for (const section of state.sections) {
        const item = section.items.find(item => item.id === itemId);
        if (item) return { section, item };
      }
      return null;
    },
  };
}
