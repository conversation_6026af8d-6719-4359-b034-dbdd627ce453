'use client';

import React, { ReactNode, useEffect } from 'react';
import { Sidebar } from './sidebar';
import { SidebarShortcuts } from './sidebar-shortcuts';
import { useInitializeSidebar } from './use-sidebar-data';
import { useSidebar } from './sidebar-context';
import { MainContent } from '@/components/content/main-content';
import { cn } from '@/lib/utils';

interface SidebarLayoutProps {
  children?: ReactNode;
  className?: string;
  showMainContent?: boolean;
}

export function SidebarLayout({ children, className, showMainContent = false }: SidebarLayoutProps) {
  const { state } = useSidebar();
  useInitializeSidebar();

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      // Auto-close sidebar on small screens when not pinned
      if (window.innerWidth < 1024 && !state.isPinned && state.isOpen) {
        // Don't auto-close, let user control
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [state.isPinned, state.isOpen]);

  return (
    <div className={cn('flex h-screen overflow-hidden', className)}>
      {/* Keyboard shortcuts handler */}
      <SidebarShortcuts />
      
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Main content */}
        <main className="flex-1 overflow-auto">
          {showMainContent ? <MainContent /> : children}
        </main>
      </div>
    </div>
  );
}

export default SidebarLayout;
