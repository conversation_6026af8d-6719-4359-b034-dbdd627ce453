'use client';

import React from 'react';
import Link from 'next/link';
import { 
  Message<PERSON>quare, 
  Pin, 
  <PERSON>nO<PERSON>, 
  <PERSON>u,
  Plus,
} from 'lucide-react';
import { IconButton } from '@/components/ui/icon-button';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useSidebar } from './sidebar-context';
import { useTenant } from '@/components/tenant/tenant-provider';
import { cn } from '@/lib/utils';

interface SidebarHeaderProps {
  className?: string;
  showLogo?: boolean;
  showControls?: boolean;
}

export function SidebarHeader({
  className,
  showLogo = true,
  showControls = true,
}: SidebarHeaderProps) {
  const { state, toggle, togglePin } = useSidebar();
  const { tenantSlug, tenant } = useTenant();

  return (
    <TooltipProvider>
      <div className={cn(
        'flex items-center justify-between p-4 border-b border-border',
        'min-h-[60px] bg-background/95 backdrop-blur-sm',
        className
      )}>
        {/* Logo and Brand */}
        {showLogo && (
          <Link
            href={`/${tenantSlug}/projects`}
            className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
          >
            <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-lg">
              <MessageSquare className="w-5 h-5 text-primary-foreground" />
            </div>
            <div className="flex flex-col">
              <span className="font-semibold text-sm text-foreground">
                {tenant?.name || 'KAVIA'}
              </span>
              <span className="text-xs text-muted-foreground">
                AI Assistant
              </span>
            </div>
          </Link>
        )}

        {/* Controls */}
        {showControls && (
          <div className="flex items-center space-x-1">
            {/* New Chat Button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  asChild
                  className="w-8 h-8 p-0"
                >
                  <Link href={`/${tenantSlug}/chat`}>
                    <Plus className="w-4 h-4" />
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>New Chat</p>
              </TooltipContent>
            </Tooltip>

            {/* Pin Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  variant="ghost"
                  onClick={togglePin}
                  aria-label={state.isPinned ? 'Unpin Sidebar' : 'Pin Sidebar'}
                  icon={state.isPinned ? (
                    <PinOff className="w-4 h-4" />
                  ) : (
                    <Pin className="w-4 h-4" />
                  )}
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>{state.isPinned ? 'Unpin Sidebar' : 'Pin Sidebar'}</p>
              </TooltipContent>
            </Tooltip>

            {/* Collapse Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  variant="ghost"
                  onClick={toggle}
                  aria-label="Toggle Sidebar"
                  icon={<Menu className="w-4 h-4" />}
                />
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle Sidebar</p>
              </TooltipContent>
            </Tooltip>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}

export default SidebarHeader;
