'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion } from 'framer-motion';
import * as LucideIcons from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from '@/components/ui/context-menu';
import { useSidebar } from './sidebar-context';
import { SidebarItem as SidebarItemType } from '@/types';
import { cn } from '@/lib/utils';

interface SidebarItemProps {
  item: SidebarItemType;
  level?: number;
  className?: string;
}

export function SidebarItem({ item, level = 0, className }: SidebarItemProps) {
  const pathname = usePathname();
  const { setActiveItem, state } = useSidebar();

  // Handle separator type
  if (item.type === 'separator') {
    return <Separator className="my-2" />;
  }

  // Get icon component
  const IconComponent = item.icon ? (LucideIcons as unknown as Record<string, React.ComponentType<{ className?: string }>>)[item.icon] : null;

  // Check if item is active
  const isActive = item.href ? pathname === item.href : state.activeItem === item.id;

  // Handle click
  const handleClick = () => {
    if (item.id) {
      setActiveItem(item.id);
    }
  };

  // Context menu actions
  const contextMenuItems = [
    {
      label: 'Rename',
      action: () => {
        // TODO: Implement rename functionality
        console.log('Rename item:', item.id);
      },
    },
    {
      label: 'Delete',
      action: () => {
        // TODO: Implement delete functionality
        console.log('Delete item:', item.id);
      },
      destructive: true,
    },
  ];

  // Item content
  const itemContent = (
    <motion.div
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
      className={cn(
        'flex items-center w-full justify-between group',
        level > 0 && 'ml-4'
      )}
    >
      <div className="flex items-center flex-1 min-w-0">
        {/* Icon */}
        {IconComponent && (
          <IconComponent className="flex-shrink-0 w-4 h-4 mr-3 text-muted-foreground" />
        )}

        {/* Label */}
        <span className="text-sm font-medium truncate text-foreground">
          {item.label}
        </span>
      </div>

      {/* Badge */}
      {item.badge && (
        <Badge variant="secondary" className="ml-2 text-xs">
          {item.badge}
        </Badge>
      )}
    </motion.div>
  );

  // Render with or without link
  const buttonElement = (
    <Button
      variant={isActive ? 'secondary' : 'ghost'}
      size="sm"
      onClick={handleClick}
      className={cn(
        'w-full justify-start h-auto py-2 px-2',
        'hover:bg-accent hover:text-accent-foreground',
        'focus-visible:ring-1 focus-visible:ring-ring',
        isActive && 'bg-accent text-accent-foreground',
        className
      )}
      asChild={!!item.href}
    >
      {item.href ? (
        <Link href={item.href}>
          {itemContent}
        </Link>
      ) : (
        itemContent
      )}
    </Button>
  );

  // Wrap with context menu for certain item types
  if (item.type === 'chat' || item.type === 'project') {
    return (
      <ContextMenu>
        <ContextMenuTrigger asChild>
          {buttonElement}
        </ContextMenuTrigger>
        <ContextMenuContent>
          {contextMenuItems.map((menuItem, index) => (
            <ContextMenuItem
              key={index}
              onClick={menuItem.action}
              className={menuItem.destructive ? 'text-destructive' : ''}
            >
              {menuItem.label}
            </ContextMenuItem>
          ))}
        </ContextMenuContent>
      </ContextMenu>
    );
  }

  return buttonElement;
}

export default SidebarItem;
