'use client';

import { useEffect, useMemo } from 'react';
import { usePathname } from 'next/navigation';
import { useTenant } from '@/components/tenant/tenant-provider';
import { useSidebarSections } from './sidebar-context';
import { SidebarSection, SidebarItem } from '@/types';

export function useSidebarData() {
  const { tenantSlug } = useTenant();
  const pathname = usePathname();
  const { addItem, removeItem } = useSidebarSections();

  // Get current project from pathname if available
  const pathParts = pathname.split('/').filter(Boolean);
  const currentProject = pathParts.length >= 2 ? pathParts[1] : null;

  // Generate default sections
  const defaultSections: SidebarSection[] = useMemo(() => [
    // Navigation Section
    {
      id: 'navigation',
      title: 'Navigation',
      collapsible: false,
      items: [
        {
          id: 'home',
          type: 'nav',
          label: 'Home',
          icon: 'Home',
          href: `/${tenantSlug}`,
          isActive: pathname === `/${tenantSlug}`,
        },
        {
          id: 'projects',
          type: 'nav',
          label: 'Projects',
          icon: 'FolderOpen',
          href: `/${tenantSlug}/projects`,
          isActive: pathname === `/${tenantSlug}/projects`,
        },
        ...(currentProject ? [
          {
            id: 'current-project-chat',
            type: 'nav' as const,
            label: 'Project Chat',
            icon: 'MessageSquarePlus',
            href: `/${tenantSlug}/${currentProject}/chat`,
            isActive: pathname.includes('/chat'),
          },
        ] : []),
      ],
    },
    
    // Recent Chats Section (only show if we're in a project context)
    ...(currentProject ? [
      {
        id: 'recent-chats',
        title: 'Recent Chats',
        collapsible: true,
        collapsed: false,
        items: [
          {
            id: 'chat-1',
            type: 'chat' as const,
            label: 'AI Code Review Assistant',
            icon: 'MessageSquare',
            href: `/${tenantSlug}/${currentProject}/chat`,
            isActive: pathname.includes('/chat'),
            badge: '5',
          },
          {
            id: 'chat-2',
            type: 'chat' as const,
            label: 'React Component Design',
            icon: 'MessageSquare',
            href: `/${tenantSlug}/${currentProject}/chat`,
            isActive: false,
          },
          {
            id: 'chat-3',
            type: 'chat' as const,
            label: 'Database Schema Planning',
            icon: 'MessageSquare',
            href: `/${tenantSlug}/${currentProject}/chat`,
            isActive: false,
          },
        ],
      },
    ] : []),
    
    // Projects Section
    {
      id: 'active-projects',
      title: 'Active Projects',
      collapsible: true,
      collapsed: false,
      items: [
        {
          id: 'project-1',
          type: 'project' as const,
          label: 'E-commerce Platform',
          icon: 'Folder',
          href: `/${tenantSlug}/project-1/chat`,
          isActive: pathname.includes('/project-1'),
          badge: currentProject === 'project-1' ? 'active' : undefined,
        },
        {
          id: 'project-2',
          type: 'project' as const,
          label: 'Mobile App Backend',
          icon: 'Folder',
          href: `/${tenantSlug}/project-2/chat`,
          isActive: pathname.includes('/project-2'),
          badge: currentProject === 'project-2' ? 'active' : undefined,
        },
        {
          id: 'project-3',
          type: 'project' as const,
          label: 'AI Dashboard',
          icon: 'Folder',
          href: `/${tenantSlug}/project-3/chat`,
          isActive: pathname.includes('/project-3'),
          badge: currentProject === 'project-3' ? 'active' : undefined,
        },
      ],
    },
  ], [tenantSlug, pathname, currentProject]);

  return {
    defaultSections,
    // Utility functions for dynamic content management
    addChat: (title: string, chatId: string) => {
      const newChat: SidebarItem = {
        id: chatId,
        type: 'chat',
        label: title,
        icon: 'MessageSquare',
        href: `/${tenantSlug}/chat/${chatId}`,
      };
      addItem('recent-chats', newChat, 0); // Add to top
    },
    
    removeChat: (chatId: string) => {
      removeItem('recent-chats', chatId);
    },
    
    addProject: (name: string, projectId: string) => {
      const newProject: SidebarItem = {
        id: projectId,
        type: 'project',
        label: name,
        icon: 'Folder',
        href: `/${tenantSlug}/projects/${projectId}`,
      };
      addItem('active-projects', newProject);
    },
    
    removeProject: (projectId: string) => {
      removeItem('active-projects', projectId);
    },
    
    updateChatTitle: (chatId: string, newTitle: string) => {
      // Find and update the chat item
      // This would need to be implemented based on your state management needs
      console.log('Update chat title:', chatId, newTitle);
    },
  };
}

// Hook to initialize sidebar with default data
export function useInitializeSidebar() {
  const { defaultSections } = useSidebarData();
  const { sections, updateSection } = useSidebarSections();

  useEffect(() => {
    // Only initialize if sections are empty
    if (sections.length === 0) {
      defaultSections.forEach(section => {
        updateSection(section.id, section);
      });
    }
  }, [defaultSections, sections.length, updateSection]);

  return { isInitialized: sections.length > 0 };
}
