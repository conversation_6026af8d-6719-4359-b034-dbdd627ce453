'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { SidebarItem } from './sidebar-item';
import { useSidebar } from './sidebar-context';
import { SidebarSection as SidebarSectionType } from '@/types';
import { cn } from '@/lib/utils';

interface SidebarSectionProps {
  section: SidebarSectionType;
  className?: string;
}

export function SidebarSection({ section, className }: SidebarSectionProps) {
  const { updateSection } = useSidebar();

  const handleToggleCollapse = () => {
    if (section.collapsible) {
      updateSection(section.id, { collapsed: !section.collapsed });
    }
  };

  const contentVariants = {
    open: {
      height: 'auto',
      opacity: 1,
    },
    closed: {
      height: 0,
      opacity: 0,
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      x: -10,
    },
    visible: {
      opacity: 1,
      x: 0,
    },
  };

  const itemsContainerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: 0.03,
      },
    },
  };

  return (
    <div className={cn('space-y-1', className)}>
      {/* Section Header */}
      {section.title && (
        <div className="flex items-center justify-between px-2 py-1">
          {section.collapsible ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleToggleCollapse}
              className="flex items-center justify-start w-full h-auto p-1 text-xs font-medium text-muted-foreground hover:text-foreground"
            >
              {section.collapsed ? (
                <ChevronRight className="w-3 h-3 mr-1" />
              ) : (
                <ChevronDown className="w-3 h-3 mr-1" />
              )}
              {section.title}
            </Button>
          ) : (
            <h3 className="px-1 text-xs font-medium text-muted-foreground uppercase tracking-wider">
              {section.title}
            </h3>
          )}
        </div>
      )}

      {/* Section Content */}
      {section.collapsible ? (
        <Collapsible open={!section.collapsed}>
          <CollapsibleContent asChild>
            <motion.div
              initial="closed"
              animate={section.collapsed ? 'closed' : 'open'}
              variants={contentVariants}
              className="overflow-hidden"
            >
              <motion.div
                initial="hidden"
                animate="visible"
                variants={itemsContainerVariants}
                className="space-y-0.5"
              >
                <AnimatePresence mode="popLayout">
                  {section.items.map((item) => (
                    <motion.div
                      key={item.id}
                      variants={itemVariants}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                      layout
                    >
                      <SidebarItem item={item} />
                    </motion.div>
                  ))}
                </AnimatePresence>
              </motion.div>
            </motion.div>
          </CollapsibleContent>
        </Collapsible>
      ) : (
        <motion.div
          initial="hidden"
          animate="visible"
          variants={itemsContainerVariants}
          className="space-y-0.5"
        >
          <AnimatePresence mode="popLayout">
            {section.items.map((item) => (
              <motion.div
                key={item.id}
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                layout
              >
                <SidebarItem item={item} />
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      )}
    </div>
  );
}

export default SidebarSection;
