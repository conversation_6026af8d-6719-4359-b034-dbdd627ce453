'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  MessageSquare, 
  Briefcase,
  PanelLeftClose,
  User,
  LogOut,
  Settings,
  MoreHorizontal,
  Trash2,
  Edit3,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { IconButton } from '@/components/ui/icon-button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useSidebar } from './sidebar-context';
import { cn } from '@/lib/utils';

interface ExpandedSidebarProps {
  className?: string;
}

export function ExpandedSidebar({ className }: ExpandedSidebarProps) {
  const { state, toggle, setActiveItem } = useSidebar();

  // Silencing TS unused warnings for icons kept for future interactions
  void Edit3;
  void Trash2;

  // Mock user data
  const user = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: '',
    initials: 'JD',
  };

  // Mock chat history data
  const recentChats = [
    { id: '1', title: 'AI Code Review Assistant', time: '2 hours ago' },
    { id: '2', title: 'React Component Design', time: '1 day ago' },
    { id: '3', title: 'Database Schema Planning', time: '2 days ago' },
    { id: '4', title: 'API Integration Help', time: '3 days ago' },
    { id: '5', title: 'Performance Optimization', time: '1 week ago' },
  ];


  const handleLogout = () => {
    console.log('Logout');
  };

  const router = useRouter();

  const handleNewChat = () => {
    setActiveItem('new-chat');
    // Navigate to new chat - you can customize this route as needed
    router.push(`/${tenantSlug}/newchat`);
  };

  const handleChatHistoryClick = () => {
    setActiveItem('chat-history');
    // Navigate to general chat history (not project-specific)
    // For now, we'll assume there's a general chat history route
    // You may need to adjust this based on your requirements
    router.push(`/${tenantSlug}/chats`);
  };

  const handleProjectsClick = () => {
    setActiveItem('projects');
    // Navigate to projects page
    router.push(`/${tenantSlug}/projects`);
  };

  const handleChatAction = (action: string, chatId: string) => {
    console.log(`${action} chat:`, chatId);
  };

  return (
    <TooltipProvider>
      <motion.div
        initial={{ width: state.collapsedWidth }}
        animate={{ width: state.width }}
        exit={{ width: state.collapsedWidth }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        className={cn(
          'fixed left-0 top-0 z-50 h-full bg-white border-r border-gray-200',
          'flex flex-col overflow-hidden shadow-lg',
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-[#F26A1B] rounded-lg flex items-center justify-center">
              <MessageSquare className="w-4 h-4 text-white" />
            </div>
            <div>
              <h1 className="text-sm font-semibold text-gray-900">KAVIA AI</h1>
              <p className="text-xs text-gray-500">Assistant</p>
            </div>
          </div>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <IconButton
                variant="ghost"
                onClick={toggle}
                className="w-8 h-8 hover:bg-gray-100"
                aria-label="Collapse Sidebar"
                icon={<PanelLeftClose className="w-4 h-4 text-gray-600" />}
              />
            </TooltipTrigger>
            <TooltipContent>
              <p>Collapse Sidebar</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Navigation Menu */}
        <div className="px-4 py-2 space-y-1">
          {/* New Chat - Elegant Style */}
          <div 
            className="flex items-center justify-between p-3 transition-colors rounded-lg cursor-pointer hover:bg-gray-50 group"
            onClick={handleNewChat}
          >
            <div className="flex items-center">
              <Plus className="w-4 h-4 mr-3 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">New Chat</span>
            </div>
            <div className="w-6 h-6 rounded-md bg-gray-100 group-hover:bg-[#F26A1B] transition-colors flex items-center justify-center">
              <Plus className="w-3 h-3 text-gray-500 group-hover:text-white" />
            </div>
          </div>

          {/* Chat History */}
          <div 
            className="flex items-center p-3 transition-colors rounded-lg cursor-pointer hover:bg-gray-50 group"
            onClick={handleChatHistoryClick}
          >
            <MessageSquare className="w-4 h-4 mr-3 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">Chats</span>
          </div>

          {/* Projects */}
          <div 
            className="flex items-center p-3 transition-colors rounded-lg cursor-pointer hover:bg-gray-50 group"
            onClick={handleProjectsClick}
          >
            <Briefcase className="w-4 h-4 mr-3 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">Projects</span>
          </div>
        </div>

        {/* Recents Section */}
        <div className="flex-1 overflow-hidden">
          <div className="px-4 py-2">
            <h3 className="mb-3 text-xs font-medium tracking-wider text-gray-500 uppercase">Recents</h3>
          </div>
          
          <ScrollArea className="flex-1 px-4">
            <div className="pb-4 space-y-1">
              <AnimatePresence>
                {recentChats.map((chat) => (
                  <motion.div
                    key={chat.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="flex items-center justify-between p-2 rounded-lg cursor-pointer group hover:bg-gray-50"
                    onClick={handleChatHistoryClick}
                  >
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900 truncate">{chat.title}</p>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <IconButton
                          variant="ghost"
                          className="w-6 h-6 opacity-0 group-hover:opacity-100"
                          aria-label="More options"
                          icon={<MoreHorizontal className="w-3 h-3" />}
                        />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-40">
                        <DropdownMenuItem onClick={() => handleChatAction('rename', chat.id)}>
                          <Edit3 className="w-3 h-3 mr-2" />
                          Rename
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleChatAction('delete', chat.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-3 h-3 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          </ScrollArea>
        </div>

        {/* User Profile Footer */}
        <div className="p-4 border-t border-gray-100">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="justify-start w-full h-auto p-2 hover:bg-gray-50"
              >
                <Avatar className="w-8 h-8 mr-3">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="text-xs bg-[#FFEFE4] text-[#F26A1B] font-medium">
                    {user.initials}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0 text-left">
                  <p className="text-sm font-medium text-gray-900 truncate">{user.name}</p>
                  <p className="text-xs text-gray-500 truncate">{user.email}</p>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem>
                <User className="w-4 h-4 mr-2" />
                Profile
              </DropdownMenuItem>
              
              <DropdownMenuItem>
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem 
                onClick={handleLogout}
                className="text-red-600 focus:text-red-600"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </motion.div>
    </TooltipProvider>
  );
}

export default ExpandedSidebar;
