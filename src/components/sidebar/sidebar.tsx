'use client';

import React from 'react';
import { useSidebar } from './sidebar-context';
import { UnifiedSidebar } from './unified-sidebar';
import { cn } from '@/lib/utils';

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const { state } = useSidebar();

  if (!state.isOpen) {
    return null;
  }

  return (
    <div className={cn('relative', className)}>
      <UnifiedSidebar />
      
      {/* Spacer for layout */}
      <div
        style={{ width: state.isExpanded ? state.width : state.collapsedWidth }}
        className="flex-shrink-0"
      />
    </div>
  );
}

export default Sidebar;
