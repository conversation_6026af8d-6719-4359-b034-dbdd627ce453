// Main sidebar exports
export { Sidebar } from './sidebar';
export { SidebarProvider, useSidebar, useSidebarSections } from './sidebar-context';
export { SidebarLayout } from './sidebar-layout';

// New collapsed/expanded components
export { CollapsedSidebar } from './collapsed-sidebar';
export { ExpandedSidebar } from './expanded-sidebar';

// Legacy component exports (for backward compatibility)
export { SidebarHeader } from './sidebar-header';
export { SidebarContent } from './sidebar-content';
export { SidebarFooter } from './sidebar-footer';
export { SidebarSection } from './sidebar-section';
export { SidebarItem } from './sidebar-item';
export { SidebarResizer } from './sidebar-resizer';
export { SidebarShortcuts } from './sidebar-shortcuts';

// Hooks
export { useSidebarData, useInitializeSidebar } from './use-sidebar-data';

// Re-export types from main types file
export type {
  SidebarItem as SidebarItemType,
  SidebarSection as SidebarSectionType,
  SidebarState,
  SidebarContextValue,
} from '@/types';
