'use client';

import React from 'react';
import Link from 'next/link';
import { 
  Settings, 
  User, 
  LogOut, 
  HelpCircle,
  Keyboard,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import { useTenant } from '@/components/tenant/tenant-provider';
import { cn } from '@/lib/utils';

interface SidebarFooterProps {
  className?: string;
  showUserProfile?: boolean;
  showThemeToggle?: boolean;
}

export function SidebarFooter({
  className,
  showUserProfile = true,
  showThemeToggle = true,
}: SidebarFooterProps) {
  const { tenantSlug } = useTenant();

  // Mock user data - replace with actual user context
  const user = {
    name: '<PERSON> Doe',
    email: '<EMAIL>',
    avatar: '',
    initials: 'JD',
  };

  const handleLogout = () => {
    // TODO: Implement logout functionality
    console.log('Logout');
  };

  const handleKeyboardShortcuts = () => {
    // TODO: Implement keyboard shortcuts modal
    console.log('Show keyboard shortcuts');
  };

  return (
    <div className={cn(
      'flex items-center justify-between p-4 border-t border-border',
      'bg-background/95 backdrop-blur-sm',
      className
    )}>
      {/* User Profile */}
      {showUserProfile && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex items-center space-x-2 w-full justify-start h-auto p-2"
            >
              <Avatar className="w-6 h-6">
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="text-xs">
                  {user.initials}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col items-start flex-1 min-w-0">
                <span className="text-sm font-medium truncate">
                  {user.name}
                </span>
                <span className="text-xs text-muted-foreground truncate">
                  {user.email}
                </span>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            <DropdownMenuItem asChild>
              <Link href={`/${tenantSlug}/profile`}>
                <User className="w-4 h-4 mr-2" />
                Profile
              </Link>
            </DropdownMenuItem>
            
            <DropdownMenuItem asChild>
              <Link href={`/${tenantSlug}/settings`}>
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Link>
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem onClick={handleKeyboardShortcuts}>
              <Keyboard className="w-4 h-4 mr-2" />
              Keyboard shortcuts
            </DropdownMenuItem>
            
            <DropdownMenuItem asChild>
              <Link href="/help">
                <HelpCircle className="w-4 h-4 mr-2" />
                Help & Support
              </Link>
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem 
              onClick={handleLogout}
              className="text-destructive focus:text-destructive"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Controls */}
      <div className="flex items-center space-x-1">
        {/* Theme Toggle */}
        {showThemeToggle && (
          <ThemeToggle />
        )}

        {/* Help */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              asChild
              className="w-8 h-8 p-0"
            >
              <Link href="/help">
                <HelpCircle className="w-4 h-4" />
              </Link>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Help & Support</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
}

export default SidebarFooter;
