'use client';

import React, { useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  MessageSquare, 
  Briefcase,
  PanelLeftOpen,
  PanelLeftClose,
  User,
  LogOut,
  Settings,
  MoreHorizontal,
  Trash2,
  Edit3,
} from 'lucide-react';
// import { Button } from '@/components/ui/button';
import { IconButton } from '@/components/ui/icon-button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useSidebar } from './sidebar-context';
import { useTenant } from '@/components/tenant/tenant-provider';
import { cn } from '@/lib/utils';

interface UnifiedSidebarProps {
  className?: string;
}

export function UnifiedSidebar({ className }: UnifiedSidebarProps) {
  const { state, toggle, setActiveItem } = useSidebar();
  const { tenantSlug } = useTenant();
  const router = useRouter();

  // Mock user data
  const user = {
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: '',
    initials: 'JD',
  };

  // Mock chat history data
  const recentChats = [
    { id: '1', title: 'AI Code Review Assistant', time: '2 hours ago' },
    { id: '2', title: 'React Component Design', time: '1 day ago' },
    { id: '3', title: 'Database Schema Planning', time: '2 days ago' },
    { id: '4', title: 'API Integration Help', time: '3 days ago' },
    { id: '5', title: 'Performance Optimization', time: '1 week ago' },
  ];

  const handleLogout = useCallback(() => {
    console.log('Logout');
  }, []);

  const handleItemClick = useCallback((itemId: string) => {
    setActiveItem(itemId);
    switch (itemId) {
      case 'projects':
        router.push(`/${tenantSlug}/projects`);
        break;
      case 'chat-history':
        router.push(`/${tenantSlug}/chats`);
        break;
      case 'new-chat':
        router.push(`/${tenantSlug}/newchat`);
        break;
      default:
        break;
    }
  }, [setActiveItem, router, tenantSlug]);

  const handleChatAction = useCallback((action: string, chatId: string) => {
    console.log(`${action} chat:`, chatId);
  }, []);

  const navItems = useMemo(() => [
    {
      id: 'new-chat',
      label: 'New Chat',
      icon: Plus,
      href: `/${tenantSlug}/project-1/chat`,
      isActive: state.activeItem === 'new-chat',
      onClick: () => handleItemClick('new-chat'),
      hasAction: true,
    },
    {
      id: 'chat-history',
      label: 'Chats',
      icon: MessageSquare,
      href: `/${tenantSlug}/project-1/chat`,
      isActive: state.activeItem === 'chat-history',
      onClick: () => handleItemClick('chat-history'),
    },
    {
      id: 'projects',
      label: 'Projects',
      icon: Briefcase,
      href: `/${tenantSlug}/projects`,
      isActive: state.activeItem === 'projects',
      onClick: () => handleItemClick('projects'),
    },
  ], [tenantSlug, state.activeItem, handleItemClick]);

  return (
    <TooltipProvider>
      <motion.div
        initial={{ width: state.isExpanded ? state.width : state.collapsedWidth }}
        animate={{ width: state.isExpanded ? state.width : state.collapsedWidth }}
        transition={{ 
          type: 'spring', 
          stiffness: 300, 
          damping: 30,
          duration: 0.3
        }}
        className={cn(
          'fixed left-0 top-0 z-50 h-full bg-white border-r border-gray-200',
          'flex flex-col overflow-hidden shadow-sm',
          className
        )}
      >
        {/* Header - Toggle icon on left, KAVIA text to the right when expanded */}
        <div className="flex items-center h-16 p-4">
          {/* Toggle button - Always on the left, fixed size */}
          <div className="flex-shrink-0 w-8 h-8">
            <Tooltip>
              <TooltipTrigger asChild>
                <IconButton
                  variant="ghost"
                  onClick={toggle}
                  className="w-8 h-8 rounded-xl hover:bg-[#FFEFE4]"
                  aria-label={state.isExpanded ? "Collapse Sidebar" : "Expand Sidebar"}
                  icon={state.isExpanded ? 
                    <PanelLeftClose className="w-4 h-4 text-[#F26A1B]" /> : 
                    <PanelLeftOpen className="w-4 h-4 text-[#F26A1B]" />
                  }
                />
              </TooltipTrigger>
              <TooltipContent side="right">
                <p>{state.isExpanded ? "Collapse Sidebar" : "Expand Sidebar"}</p>
              </TooltipContent>
            </Tooltip>
          </div>
          
          {/* KAVIA text - Only when expanded, positioned to the right of toggle */}
          <AnimatePresence>
            {state.isExpanded && (
              <motion.div
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: 'auto' }}
                exit={{ opacity: 0, width: 0 }}
                transition={{ duration: 0.2, ease: 'easeInOut' }}
                className="ml-3 overflow-hidden"
              >
                <h1 className="text-sm font-semibold text-gray-900 whitespace-nowrap">KAVIA</h1>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Navigation Menu - Original Style with Brand Colors */}
        <div className="px-3 py-2 space-y-1">
          {/* New Chat - Original style with action button */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div 
                className={cn(
                  'flex items-center transition-all duration-200 rounded-lg cursor-pointer group relative',
                  'hover:bg-[#FFEFE4] p-3',
                  navItems[0].isActive 
                    ? 'bg-[#FFEFE4] text-[#F26A1B]' 
                    : 'text-gray-600 hover:text-[#F26A1B]'
                )}
                onClick={() => handleItemClick('new-chat')}
              >
                {/* Icon - Always visible and fixed position */}
                <div className="flex items-center justify-center flex-shrink-0 w-4 h-4">
                  <Plus className="w-4 h-4" />
                </div>
                
                {/* Text and Action - Only visible when expanded */}
                <AnimatePresence>
                  {state.isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, width: 0 }}
                      animate={{ opacity: 1, width: 'auto' }}
                      exit={{ opacity: 0, width: 0 }}
                      transition={{ duration: 0.2, ease: 'easeInOut' }}
                      className="flex items-center justify-between flex-1 ml-3 overflow-hidden"
                    >
                      <span className="text-sm font-medium whitespace-nowrap">
                        New Chat
                      </span>
                      
                      {/* Action button - Original Style */}
                      <div className="w-6 h-6 rounded-md bg-gray-100 group-hover:bg-[#F26A1B] transition-colors flex items-center justify-center flex-shrink-0">
                        <Plus className="w-3 h-3 text-gray-500 group-hover:text-white" />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </TooltipTrigger>
            <TooltipContent side="right">
              <p>New Chat</p>
            </TooltipContent>
          </Tooltip>

          {/* Other navigation items */}
          {navItems.slice(1).map((item) => {
            const IconComponent = item.icon;
            
            return (
              <Tooltip key={item.id}>
                <TooltipTrigger asChild>
                  <div 
                    className={cn(
                      'flex items-center transition-all duration-200 rounded-lg cursor-pointer group relative',
                      'hover:bg-[#FFEFE4] p-3',
                      item.isActive 
                        ? 'bg-[#FFEFE4] text-[#F26A1B]' 
                        : 'text-gray-600 hover:text-[#F26A1B]'
                    )}
                    onClick={item.onClick}
                  >
                    {/* Icon - Always visible and fixed position */}
                    <div className="flex items-center justify-center flex-shrink-0 w-4 h-4">
                      <IconComponent className="w-4 h-4" />
                    </div>
                    
                    {/* Text - Only visible when expanded */}
                    <AnimatePresence>
                      {state.isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, width: 0 }}
                          animate={{ opacity: 1, width: 'auto' }}
                          exit={{ opacity: 0, width: 0 }}
                          transition={{ duration: 0.2, ease: 'easeInOut' }}
                          className="ml-3 overflow-hidden"
                        >
                          <span className="text-sm font-medium whitespace-nowrap">
                            {item.label}
                          </span>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{item.label}</p>
                </TooltipContent>
              </Tooltip>
            );
          })}
        </div>

        {/* Recents Section - Only visible when expanded, Claude style */}
        <AnimatePresence>
          {state.isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
              className="flex-1 overflow-hidden"
            >
              <div className="px-3 py-4">
                <h3 className="mb-3 text-sm font-medium text-gray-500">Recents</h3>
              </div>
              
              <ScrollArea className="flex-1 px-3">
                <div className="pb-4 space-y-1">
                  <AnimatePresence>
                    {recentChats.map((chat) => (
                      <motion.div
                        key={chat.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="flex items-center justify-between p-2 rounded-lg cursor-pointer group hover:bg-[#FFEFE4]"
                        onClick={() => handleItemClick('chat-history')}
                      >
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-gray-700 truncate">{chat.title}</p>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <IconButton
                              variant="ghost"
                              className="w-6 h-6 opacity-0 group-hover:opacity-100"
                              aria-label="More options"
                              icon={<MoreHorizontal className="w-3 h-3" />}
                            />
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-40">
                            <DropdownMenuItem onClick={() => handleChatAction('rename', chat.id)}>
                              <Edit3 className="w-3 h-3 mr-2" />
                              Rename
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleChatAction('delete', chat.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="w-3 h-3 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </ScrollArea>
            </motion.div>
          )}
        </AnimatePresence>

        {/* User Profile Footer - At the very bottom like Claude */}
        <div className="p-3 mt-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className={cn(
                'flex items-center transition-colors rounded-lg cursor-pointer hover:bg-[#FFEFE4] group p-2'
              )}>
                <Avatar className="flex-shrink-0 w-8 h-8">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="text-xs bg-[#FFEFE4] text-[#F26A1B] font-medium">
                    {user.initials}
                  </AvatarFallback>
                </Avatar>
                
                <AnimatePresence>
                  {state.isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, width: 0 }}
                      animate={{ opacity: 1, width: 'auto' }}
                      exit={{ opacity: 0, width: 0 }}
                      transition={{ duration: 0.2, ease: 'easeInOut' }}
                      className="flex-1 min-w-0 ml-3 overflow-hidden text-left"
                    >
                      <p className="text-sm font-medium text-gray-700 truncate">{user.name}</p>
                      <p className="text-xs text-gray-500 truncate">{user.email}</p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem>
                <User className="w-4 h-4 mr-2" />
                Profile
              </DropdownMenuItem>
              
              <DropdownMenuItem>
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem 
                onClick={handleLogout}
                className="text-red-600 focus:text-red-600"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </motion.div>
    </TooltipProvider>
  );
}

export default UnifiedSidebar;
