import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// List of valid tenants (in a real app, this would come from a database)
const VALID_TENANTS = ['demo', 'acme', 'kavia', 'test'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files, API routes, Next.js internals, and special pages
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/static') ||
    pathname.startsWith('/theme-demo') ||
    pathname === '/' ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Extract tenant from the URL path
  const pathSegments = pathname.split('/').filter(Boolean);
  const tenant = pathSegments[0];

  // If no tenant in path, redirect to a default tenant or show tenant selection
  if (!tenant) {
    // No tenant at root (except "/" which is handled above). Redirect to default tenant
    return NextResponse.redirect(new URL('/test/projects', request.url));
  }

  // Validate tenant
  if (!VALID_TENANTS.includes(tenant)) {
    // In a real app, you might want to show a 404 or tenant not found page
    return NextResponse.redirect(new URL('/test/projects', request.url));
  }

  // Add tenant to headers for use in components
  const response = NextResponse.next();
  response.headers.set('x-tenant', tenant);

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
