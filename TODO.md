# Chat UX Enhancement TODO

Approved plan: Implement smooth transition from New Chat to Chat Interface with compact composer and thinking state.

Tasks:
1. State and wiring
   - [x] Create `src/store/chat.ts` zustand store:
     - messages, status
     - startNewChat, sendMessage, completeAssistant, reset
2. Chat interface
   - [x] Create `src/components/content/chat-view.tsx`:
     - Ren<PERSON> thread (user right, assistant left with accent)
     - Thinking indicator with Spinner
     - Auto-scroll to bottom
     - Bottom sticky compact composer using ChatInput (size=sm, variant=inline)
3. Composer reuse with compact variant
   - [x] Refactor `src/components/content/chat-input.tsx`:
     - Add props: `variant: 'landing' | 'inline'`, `size: 'md' | 'sm'`, `className?`
     - Keep landing layout identical
     - Provide inline layout for chat view (no absolute positioning)
4. Routing/state change
   - [x] Update `src/components/content/main-content.tsx`:
     - Add `'chat'` case rendering `<ChatView />`
   - [x] Update `src/components/content/new-chat.tsx`:
     - On send: `chatStore.startNewChat(value, files)` then `setActiveItem('chat')`
5. Polish
   - [x] Subtle entrance animation for first user bubble
   - [x] Responsive checks and styling parity

Verification:
- Landing page unchanged visually.
- Enter on landing input transitions to chat view with initial user bubble and thinking state.
- Composer in chat view matches style, smaller size, supports attachments and character counter.
